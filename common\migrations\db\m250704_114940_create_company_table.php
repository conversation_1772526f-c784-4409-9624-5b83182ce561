<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%company}}`.
 */
class m250704_114940_create_company_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp(): void
    {
        $this->createTable('{{%company}}', [
            'id' => $this->primaryKey(),
            'logo_id' =>  $this->integer(),
            'name' =>  $this->string(),
            'summary' =>  $this->text(),
            'address' =>  $this->string(),
            'added_at' =>  $this->date(),
            'status' =>  $this->integer(),
            'created_at' =>  $this->integer(),
            'updated_at' =>  $this->integer(),
            'deleted_at' =>  $this->integer(),
            'created_by' =>  $this->integer(),
            'updated_by' =>  $this->integer(),
            'deleted_by' =>  $this->integer(),
        ]);

        $this->createIndex(
            'idx-company-logo_id',
            'company',
            'logo_id'
        );

        $this->addForeignKey(
            'fk-company-logo_id',
            'company',
            'logo_id',
            'file',
            'id'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown(): void
    {
        $this->dropTable('{{%company}}');
    }
}
