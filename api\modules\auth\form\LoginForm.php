<?php

namespace api\modules\auth\form;

use common\base\BaseModel;
use common\models\User;
use yii\base\Exception;

class LoginForm extends BaseModel
{
    public ?string $username = null;
    public ?string $password = null;
    private ?User $_user = null;

    public function rules(): array
    {
        return [
            [['username', 'password'], 'required'],
            [['username', 'password'], 'trim'],
            [['username'], 'string', 'min' => 5, 'max' => 255],
            ['password', 'string', 'min' => 8],
            [['password'], 'checkPassword'],
        ];
    }

    public function checkPassword($attribute): void
    {
        if (!$this->hasErrors()) {
            $user = $this->getUser();
            if (!$user || !$user->validatePassword($this->password)) {
                $this->addError($attribute, t('Неверное имя пользователя или пароль.'));
            }
        }
    }

    /**
     * @throws Exception
     */
    public function getResult(): bool|string
    {
        $user = $this->getUser();
        if (!$user) {
            $this->addError('*', t('Неверное имя пользователя или пароль.'));
            return false;
        }
        $user->logged_at = time();
        if (!$user->save(false)) // behaviorda access token generatsiya qilingan
            throw new Exception('Ошибка генерации токена');

        return $user->access_token;
    }

    public function getUser(): ?User
    {
        if ($this->_user === null) {
            $this->_user = User::findOne(['username' => $this->username,'status' => User::STATUS_ACTIVE]);
        }
        return $this->_user;
    }
}