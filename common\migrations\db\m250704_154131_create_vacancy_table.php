<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%vacancy}}`.
 */
class m250704_154131_create_vacancy_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp(): void
    {
        $this->createTable('{{%vacancy}}', [
            'id' => $this->primaryKey(),
            'company_id' => $this->integer(),
            'category_type_id' => $this->integer(),
            'work_type_id' => $this->integer(),
            'title_uz' => $this->string(),
            'title_ru' => $this->string(),
            'title_en' => $this->string(),
            'title_uzk' => $this->string(),
            'salary_from' => $this->bigInteger()->unsigned(),
            'salary_to' => $this->bigInteger()->unsigned(),
            'currency_code' => $this->string(),
            'state' => $this->integer(),
            'status' => $this->integer(),
            'experience' => $this->smallInteger(2)->unsigned(),
            'degree' => $this->string(),
            'published_at' => $this->integer()->unsigned(),
            'description_uz' => $this->text(),
            'description_ru' => $this->text(),
            'description_en' => $this->text(),
            'description_uzk' => $this->text(),
            'location' => $this->string(),
            'latitude' => $this->string(),
            'longitude' => $this->string(),
            'created_at' => $this->integer()->unsigned(),
            'updated_at' => $this->integer()->unsigned(),
            'deleted_at' => $this->integer()->unsigned(),
            'created_by' => $this->integer(),
            'updated_by' => $this->integer(),
            'deleted_by' => $this->integer(),
        ]);

        $this->createIndex(
            'idx-vacancy-company_id',
            'vacancy',
            'company_id'
        );

        $this->addForeignKey(
            'fk-vacancy-company_id',
            'vacancy',
            'company_id',
            'company',
            'id'
        );

        $this->addForeignKey(
            'fk-vacancy-category_type_id',
            'vacancy',
            'category_type_id',
            'job_specification',
            'id',
            'CASCADE',
            'CASCADE',
        );

        $this->createIndex(
            'idx-vacancy-work_type_id',
            'vacancy',
            'work_type_id'
        );
        $this->addForeignKey(
            'fk-vacancy-work_type_id',
            'vacancy',
            'work_type_id',
            'job_specification',
            'id',
            'CASCADE',
            'CASCADE',
        );

        $this->createIndex(
            'idx-vacancy-state',
            'vacancy',
            'state'
        );

        $this->createIndex(
            'idx-vacancy-status',
            'vacancy',
            'status'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown(): void
    {
        $this->dropTable('{{%vacancy}}');
    }
}
