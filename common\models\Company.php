<?php

namespace common\models;

use common\base\BaseActiveRecord;
use Yii;

/**
 * This is the model class for table "company".
 *
 * @property int $id
 * @property int|null $logo_id
 * @property string|null $name
 * @property string|null $summary
 * @property string|null $address
 * @property string|null $added_at
 * @property int|null $status
 * @property int|null $created_at
 * @property int|null $updated_at
 * @property int|null $deleted_at
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property int|null $deleted_by
 *
 * @property Vacancy[] $vacancies
 */
class Company extends BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName(): string
    {
        return 'company';
    }

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [['logo_id', 'status', 'created_at', 'updated_at', 'deleted_at', 'created_by', 'updated_by', 'deleted_by'], 'default', 'value' => null],
            [['logo_id', 'status', 'created_at', 'updated_at', 'deleted_at', 'created_by', 'updated_by', 'deleted_by'], 'integer'],
            [['summary'], 'string'],
            [['added_at'], 'safe'],
            [['name', 'address'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels(): array
    {
        return [
            'id' => 'ID',
            'logo_id' => 'Logo ID',
            'name' => 'Name',
            'summary' => 'Summary',
            'address' => 'Address',
            'added_at' => 'Added At',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'deleted_at' => 'Deleted At',
            'created_by' => 'Created By',
            'updated_by' => 'Updated By',
            'deleted_by' => 'Deleted By',
        ];
    }

    /**
     * Gets query for [[Vacancies]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getVacancies()
    {
        return $this->hasMany(Vacancy::class, ['company_id' => 'id']);
    }
}
