<?php

namespace api\modules\common\forms;

use api\modules\common\resources\PartnerResource;

class PartnerUpdateForm extends PartnerForm
{
    public function __construct(
        public ?PartnerResource $model = null,
        $config = []
    ) {
        parent::__construct($model, $config);
        
        // Load existing data
        if ($this->model) {
            $this->name_uz = $this->model->name_uz;
            $this->name_ru = $this->model->name_ru;
            $this->name_en = $this->model->name_en;
            $this->name_uzk = $this->model->name_uzk;
            $this->description_uz = $this->model->description_uz;
            $this->description_ru = $this->model->description_ru;
            $this->description_en = $this->model->description_en;
            $this->description_uzk = $this->model->description_uzk;
            $this->logo_id = $this->model->logo_id;
            $this->website = $this->model->website;
            $this->phone = $this->model->phone;
            $this->email = $this->model->email;
            $this->address_uz = $this->model->address_uz;
            $this->address_ru = $this->model->address_ru;
            $this->address_en = $this->model->address_en;
            $this->address_uzk = $this->model->address_uzk;
            $this->sort_order = $this->model->sort_order;
            $this->status = $this->model->status;
        }
    }
}
