<?php

namespace common\models;

use common\base\BaseActiveRecord;
use Yii;

/**
 * This is the model class for table "job_specification".
 *
 * @property int $id
 * @property int $icon_id
 * @property string|null $name_uz
 * @property string|null $name_ru
 * @property string|null $name_en
 * @property string|null $name_uzk
 * @property int|null $type
 * @property int|null $created_at
 * @property int|null $updated_at
 * @property int|null $deleted_at
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property int|null $deleted_by
 *
 * @property Vacancy[] $vacancies
 * @property Vacancy[] $vacancies0
 */
class JobSpecification extends BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'job_specification';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['icon_id', 'type', 'created_at', 'updated_at', 'deleted_at', 'created_by', 'updated_by', 'deleted_by'], 'default', 'value' => null],
            [['type', 'created_at', 'updated_at', 'deleted_at', 'created_by', 'updated_by', 'deleted_by'], 'integer'],
            [['name_uz', 'name_ru', 'name_en', 'name_uzk'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'icon_id' => 'Icon ID',
            'name_uz' => 'Name Uz',
            'name_ru' => 'Name Ru',
            'name_en' => 'Name En',
            'name_uzk' => 'Name Uzk',
            'type' => 'Type',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'deleted_at' => 'Deleted At',
            'created_by' => 'Created By',
            'updated_by' => 'Updated By',
            'deleted_by' => 'Deleted By',
        ];
    }

    /**
     * Gets query for [[Vacancies]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getVacancies()
    {
        return $this->hasMany(Vacancy::class, ['job_category_id' => 'id']);
    }

    /**
     * Gets query for [[Vacancies0]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getVacancies0()
    {
        return $this->hasMany(Vacancy::class, ['job_work_id' => 'id']);
    }
}
