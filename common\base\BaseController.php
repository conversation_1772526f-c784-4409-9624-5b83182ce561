<?php

namespace common\base;

use Yii;
use yii\filters\auth\HttpBearerAuth;
use yii\filters\ContentNegotiator;
use yii\filters\Cors;
use yii\filters\RateLimiter;
use yii\rest\Controller;
use yii\rest\OptionsAction;
use yii\web\Response;

class BaseController extends Controller
{
    public function behaviors(): array
    {
        return [
                'corsFilter' => [
                    'class' => Cors::class,
                    'cors' => [
                        // restrict access to
                        'Origin' => ['http://localhost:3000','http://gsxba.uz','https://gsxba.uz'],
                        // Allow only POST and PUT methods
                        'Access-Control-Request-Method' => ['GET', 'HEAD', 'POST', 'PUT'],
                        // Allow only headers 'X-Wsse'
                        'Access-Control-Request-Headers' => ['Origin', 'Content-Type', 'X-Auth-Token', 'Authorization', 'Accept', 'Referer', 'User-Agent', 'Headers'],
                        // Allow credentials (cookies, authorization headers, etc.) to be exposed to the browser
                        'Access-Control-Allow-Credentials' => true,
                        // Allow OPTIONS caching
                        'Access-Control-Max-Age' => 3600,
                        // Allow the X-Pagination-Current-Page header to be exposed to the browser.
                        // 'Access-Control-Expose-Headers' => ['Origin', 'Content-Type', 'X-Auth-Token', 'Authorization'],
                    ],
                ],
                [
                    'class' => ContentNegotiator::class,
                    'languages' => ['uz','ru','en','uzk'],
                    'formats' => [
                        'application/json' => Response::FORMAT_JSON,
                    ],
                ],
                'authenticator' => [
                    'class' => HttpBearerAuth::class,
                    'except' => ['login', 'index', 'view'],
                    'optional' => []
                ],
                'rateLimiter' => [
                    'class' => RateLimiter::class,
                ],
            ];
    }

    public function actionOptions(): bool
    {
        return true;
    }

    public function actions(): array
    {
        return [
            'options' => [
                'class' => OptionsAction::class
            ]
        ];
    }

    protected function sendResponse(BaseModel $model, $params = []): array
    {
        $model->load($params, '');

        if ($model->validate()) {
            $result = $model->getResult();

            if (!$result && !is_array($result)) {
                Yii::$app->response->statusCode = 422;
            }

            return [
                'result' => $result,
                'errors' => $model->errors
            ];
        } else {

            Yii::$app->response->statusCode = 422;

            return [
                'result' => null,
                'errors' => $model->errors,
            ];
        }
    }

    protected function sendModel($model): array
    {
        return [
            'result' => $model,
            'errors' => null
        ];
    }
}