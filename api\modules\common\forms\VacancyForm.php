<?php

namespace api\modules\common\forms;

use api\modules\common\resources\CompanyResource;
use api\modules\common\resources\JobSpecificationResource;
use api\modules\common\resources\VacancyResource;
use common\base\BaseModel;
use common\enums\CurrencyEnum;
use common\enums\JobSpecificationEnum;
use common\enums\StatusEnum;

class VacancyForm extends BaseModel
{
    public ?int $company_id = null;
    public ?int $category_type_id = null;
    public ?int $work_type_id = null;
    public ?string $title_uz = null;
    public ?string $title_ru = null;
    public ?string $title_en = null;
    public ?string $title_uzk = null;
    public ?string $salary_from = null;
    public ?string $salary_to = null;
    public ?string $currency_code = null;
    public ?int $status = null;
    public ?int $experience = null;
    public ?string $degree = null;
    public ?string $published_at = null; // datetime
    public ?string $description_uz = null;
    public ?string $description_ru = null;
    public ?string $description_en = null;
    public ?string $description_uzk = null;
    public ?string $location = null;
    public ?string $latitude = null;
    public ?string $longitude = null;
    public function __construct(
        public ?VacancyResource $model = null,
        $config = [],
    )
    {
        parent::__construct($config);
    }

    public function rules(): array
    {
        return [
            [
                ['category_type_id', 'work_type_id', 'title_uz', 'title_ru', 'title_en', 'salary_from', 'salary_to', 'status', 'experience','degree', 'description_uz', 'description_ru', 'description_en', 'location', 'latitude','longitude','published_at'], 'required'
            ],
            ['currency_code','default','value'=> CurrencyEnum::CURRENCY_CODE_UZS],
            [['category_type_id', 'work_type_id','company_id'],'integer'],
            [['title_uz', 'title_ru', 'title_en', 'title_uzk','degree','location', 'latitude','longitude',],'string', 'max' => 255],
            [['description_uz', 'description_ru', 'description_en', 'description_uzk',], 'string'],
            ['published_at', 'date', 'format' => 'php:d.m.Y H:i'],
            ['currency_code', 'in', 'range' => CurrencyEnum::CURRENCY_CODE_LIST],
            ['category_type_id','exist', 'targetClass' => JobSpecificationResource::class, 'targetAttribute' => ['category_type_id' => 'id'],'filter' => ['type' => JobSpecificationEnum::SPECIFICATION_CATEGORY_TYPE]],
            ['work_type_id','exist', 'targetClass' => JobSpecificationResource::class, 'targetAttribute' => ['work_type_id' => 'id'],'filter' => ['type' => JobSpecificationEnum::SPECIFICATION_WORK_TYPE]],
            [['salary_from', 'salary_to',], 'integer', 'min' => 0],
            ['salary_from', 'compare', 'compareAttribute' => 'salary_to', 'operator' => '<'],
            ['status','in','range' => StatusEnum::STATUS_LIST],
            ['experience', 'integer', 'min' => 0, 'max' => 99],
            ['company_id','exist','targetClass' => CompanyResource::class, 'targetAttribute' => ['company_id' => 'id'], 'filter' => ['deleted_at' => null]],
            [['title_uzk', 'description_uzk'],'safe']
        ];
    }

    public function getResult(): bool
    {
        $this->model->attributes = [
            'company_id' => $this->company_id,
            'category_type_id' => $this->category_type_id,
            'work_type_id' => $this->work_type_id,
            'title_uz' => $this->title_uz,
            'title_ru' => $this->title_ru,
            'title_en' => $this->title_en,
            'title_uzk' => $this->title_uz,
            'salary_from' => $this->salary_from,
            'salary_to' => $this->salary_to,
            'currency_code' => $this->currency_code,
            'status' => $this->status,
            'experience' => $this->experience,
            'degree' => $this->degree,
            'published_at' => $this->published_at ? strtotime($this->published_at) : null,
            'description_uz' => $this->description_uz,
            'description_ru' => $this->description_ru,
            'description_en' => $this->description_en,
            'description_uzk' => $this->description_uzk,
            'location' => $this->location,
            'latitude' => $this->latitude,
            'longitude' => $this->longitude,
        ];
        if (!$this->model->save())
        {
            $this->addErrors($this->model->errors);
            return false;
        }
        return true;
    }
}