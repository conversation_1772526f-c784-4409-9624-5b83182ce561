<?php

namespace api\modules\common\resources;

use common\models\Page;

class PageResource extends Page
{
    public function fields(): array
    {
        return [
            'id',
            'slug',
            'title',
            'body',
            'type',
            'published_at' => function (PageResource $model) {
                return $model->published_at ? date('d.m.Y', $model->published_at) : null;
            },
        ];
    }
}