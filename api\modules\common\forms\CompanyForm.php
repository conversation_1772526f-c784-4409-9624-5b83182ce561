<?php

namespace api\modules\common\forms;

use api\modules\common\resources\CompanyResource;
use api\modules\common\resources\FileResource;
use common\base\BaseModel;
use common\enums\StatusEnum;

class CompanyForm extends BaseModel
{
    public ?int $logo_id = null;
    public ?string $name = null;
    public ?string $summary = null;
    public ?string $address = null;
    public ?int $status = null;

    public function __construct(
        public ?CompanyResource $model = null,
        $config = [],
    )
    {
        parent::__construct($config);
    }

    public function rules(): array
    {
        return [
            [['logo_id', 'name', 'summary', 'address', 'status'], 'required'],
            [['name', 'summary', 'address',], 'trim'],
            [['name', 'summary','address',], 'string', 'max' => 255],
            [['logo_id', 'status'], 'integer'],
            ['logo_id','exist', 'targetClass' => FileResource::class, 'targetAttribute' => ['logo_id' => 'id'], 'filter' => ['deleted_at' => null]],
            ['status', 'in', 'range' => StatusEnum::STATUS_LIST],
            [
                'name', 'unique', 'filter' => function  ($query) {
                    if (!$this->model->isNewRecord)
                    {
                        $query->andWhere(['<>', 'id', $this->model->id]);
                    }
                    $query->andWhere(['deleted_at' => null]);
                } ,'targetClass' => CompanyResource::class, 'targetAttribute' => 'name',
            ],
        ];
    }

    public function getResult(): bool
    {
        $this->model->attributes = [
            'logo_id' => $this->logo_id,
            'name' => $this->name,
            'summary' => $this->summary,
            'address' => $this->address,
            'status' => $this->status,
        ];

        if ($this->model->isNewRecord) {
            $this->model->added_at = date('Y-m-d');
        }

        if (!$this->model->save()) {
            $this->addErrors($this->model->getErrors());
            return false;
        }

        return true;
    }
}