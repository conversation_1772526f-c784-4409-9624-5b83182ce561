<?php

namespace tests\frontend\functional;

use common\models\User;
use tests\frontend\_pages\SignupPage;

class SignupCest
{

    /**
     * This method is called before each cest class test method
     * @param \Codeception\Event\TestEvent $event
     */
    public function _before($event)
    {

    }

    /**
     * This method is called after each cest class test method, even if test failed.
     * @param \Codeception\Event\TestEvent $event
     */
    public function _after($event)
    {
        User::deleteAll([
            'email' => '<EMAIL>',
            'username' => 'tester',
        ]);
    }

    /**
     * This method is called when test fails.
     * @param \Codeception\Event\FailEvent $event
     */
    public function _fail($event)
    {

    }

    /**
     *
     * @param \tests\frontend\FunctionalTester $I
     * @param \Codeception\Scenario $scenario
     */
    public function testUserSignup($I, $scenario)
    {
        $I->wantTo('ensure that signup works');

        $signupPage = SignupPage::openBy($I);
        $I->see('Sign up', 'h1');

        $I->amGoingTo('submit signup form with no data');

        $signupPage->submit([]);

        $I->expectTo('see validation errors');
        $I->see('Username cannot be blank.', '.alert.alert-danger');
        $I->see('E-mail cannot be blank.', '.alert.alert-danger');
        $I->see('Password cannot be blank.', '.alert.alert-danger');
        $I->see('Confirm Password cannot be blank.', '.alert.alert-danger');

        $I->amGoingTo('submit signup form with not correct email');
        $signupPage->submit([
            'username' => 'tester',
            'email' => 'tester.email',
            'password' => 'tester_password',
            'password_confirm' => 'tester_password',
        ]);

        $I->expectTo('see that email address is wrong');
        $I->dontSee('Username cannot be blank.', '.alert.alert-danger');
        $I->dontSee('Password cannot be blank.', '.alert.alert-danger');
        $I->dontSee('Password Confirm cannot be blank.', '.alert.alert-danger');
        $I->see('E-mail is not a valid email address.', '.alert.alert-danger');


        $I->amGoingTo('submit signup form with correct email');
        $signupPage->submit([
            'username' => 'tester',
            'email' => '<EMAIL>',
            'password' => 'tester_password',
            'password_confirm' => 'tester_password',
        ]);

        $I->expectTo('see that user is created');
        $I->seeRecord('common\models\User', [
            'username' => 'tester',
            'email' => '<EMAIL>',
        ]);

//        $I->expectTo('see that user logged in');  // TODO: could not make these to work.
//        $I->seeLink('tester');
//        $I->seeLink('Logout');
    }
}
