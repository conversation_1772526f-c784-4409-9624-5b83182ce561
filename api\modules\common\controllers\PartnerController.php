<?php

namespace api\modules\common\controllers;

use api\modules\common\filters\PartnerFilter;
use api\modules\common\forms\PartnerDeleteForm;
use api\modules\common\forms\PartnerForm;
use api\modules\common\forms\PartnerUpdateForm;
use api\modules\common\resources\PartnerResource;
use common\base\BaseController;
use Yii;
use yii\web\NotFoundHttpException;

class PartnerController extends BaseController
{
    /**
     * Get list of partners with filtering
     * @return array
     */
    public function actionIndex(): array
    {
        return $this->sendResponse(
            new PartnerFilter(),
            Yii::$app->request->queryParams
        );
    }

    /**
     * Get single partner by ID
     * @param int $id
     * @return array
     * @throws NotFoundHttpException
     */
    public function actionView($id): array
    {
        return $this->sendModel($this->findOne($id));
    }

    /**
     * Create new partner
     * @return array
     */
    public function actionCreate(): array
    {
        return $this->sendResponse(
            new PartnerForm(new PartnerResource()),
            Yii::$app->request->bodyParams
        );
    }

    /**
     * Update existing partner
     * @param int $id
     * @return array
     * @throws NotFoundHttpException
     */
    public function actionUpdate($id): array
    {
        return $this->sendResponse(
            new PartnerUpdateForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }

    /**
     * Delete partner (soft delete)
     * @param int $id
     * @return array
     * @throws NotFoundHttpException
     */
    public function actionDelete($id): array
    {
        return $this->sendResponse(
            new PartnerDeleteForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }

    /**
     * Find partner by ID
     * @param int $id
     * @return PartnerResource
     * @throws NotFoundHttpException
     */
    protected function findOne($id): PartnerResource
    {
        $model = PartnerResource::findOne(['id' => $id, 'deleted_at' => null]);
        if (!$model) {
            throw new NotFoundHttpException('Partner topilmadi.');
        }
        return $model;
    }
}