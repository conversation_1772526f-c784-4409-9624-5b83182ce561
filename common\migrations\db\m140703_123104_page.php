<?php

use common\enums\PageEnum;
use yii\db\Migration;

class m140703_123104_page extends Migration
{
    /**
     * @return bool|void
     */
    public function up()
    {
        $this->createTable('{{%page}}', [
            'id' => $this->primaryKey(),
            'slug' => $this->string(2048)->notNull(),
            'title' => $this->string(512)->notNull(),
            'body' => $this->text()->notNull(),
            'view' => $this->string(),
            'type' => $this->smallInteger()->defaultValue(PageEnum::PAGE_TYPE_NEWS),
            'published_at' => $this->integer(),
            'status' => $this->smallInteger()->notNull(),
            'created_at' => $this->integer(),
            'updated_at' => $this->integer(),
            'deleted_at' => $this->integer(),
            'created_by' => $this->integer(),
            'updated_by' => $this->integer(),
            'deleted_by' => $this->integer(),
        ]);
    }

    /**
     * @return bool|void
     */
    public function down()
    {
        $this->dropTable('{{%page}}');
    }
}
