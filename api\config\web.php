<?php

use common\components\log\ErrorHandler;

return [
    'homeUrl' => Yii::getAlias('@apiUrl'),
    'controllerNamespace' => 'api\controllers',
    'components' => [
        'errorHandler' => [
            'class' => ErrorHandler::class,
            'errorAction' => 'site/error'
        ],
        'request' => [
            'enableCookieValidation' => false,
            'parsers' => [
                'application/json' => 'yii\web\JsonParser',
            ]
        ],
        'user' => [
            'class' => yii\web\User::class,
            'identityClass' => common\models\User::class,
            'loginUrl' => ['/auth/user/login'],
            'enableAutoLogin' => true,
            'as afterLogin' => common\behaviors\LoginTimestampBehavior::class,
        ],
        'response' => [
            'on beforeSend' => function ($event) {
                $headers = $event->sender->headers;
                $headers->set('X-Frame-Options', 'DENY');
            },
        ],
    ],
    'modules' => [
        'auth' => [
            'class' => api\modules\auth\Module::class,
        ],
        'common' => [
            'class' => api\modules\common\Module::class,
        ],
        'file' => [
            'class' => api\modules\file\Module::class,
        ],
    ],
];
