<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message/extract' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return [
    'About' => '关于',
    'Account Settings' => '帐号设置',
    'Articles' => '文章',
    'Backend' => '后台',
    'Body' => '内容',
    'Check your email for further instructions.' => '请检查您的邮件',
    'Confirm Password' => '密码确认',
    'Contact' => '联系',
    'E-mail' => 'Email',
    'Email' => 'Email',
    'Error while oauth process.' => 'oauth进程错误',
    'Female' => '女性',
    'Home' => '主页',
    'Incorrect username or password.' => '用户名或密码错误',
    'Language' => '语言',
    'Log in with' => 'Log in with',
    'Login' => '登录',
    'Logout' => '退出',
    'Male' => '男性',
    'Name' => '名字',
    'Need an account? Sign up.' => '需要一个帐号 ？ 注册',
    'New password was saved.' => '新密码保存成功',
    'Page not found' => '页面没找到',
    'Password' => '密码',
    'Password reset for {name}' => '为{name}重置密码',
    'Profile settings' => '资料设置',
    'Remember Me' => '记住我',
    'Request password reset' => '请求重置密码',
    'Reset password' => '重置密码',
    'Settings' => '设置',
    'Sign up with' => '注册',
    'Signup' => '注册',
    'Sorry, we are unable to reset password for email provided.' => '根据您提供的email无法充值密码',
    'Subject' => '主题',
    'Submit' => '提交',
    'Thank you for contacting us. We will respond to you as soon as possible.' => '感谢你您联系我们，我们会尽快回复',
    'There was an error sending email.' => '发送邮件时发生错误',
    'This email address has already been taken.' => '此邮件已被占用',
    'This username has already been taken.' => '用户名已经被占用',
    'Update' => '更新',
    'User Settings' => '用户设置',
    'Username' => '用户名',
    'Username or email' => '用户名或email',
    'Verification Code' => '验证码',
    'We already have a user with email {email}' => '我们已经有用户使用了该邮箱',
    'Welcome to {app-name}. Email with your login information was sent to your email.' => '欢迎来到{app-name}. 您的登录信息我们已经发送到了您的注册邮箱.',
    'Your account has been successfully saved' => '您的帐号保存成功',
    '{app-name} | Your login information' => '{app-name} | 您的登录信息',
    'Activation email' => '',
    'Archive' => '',
    'Categories' => '',
    'Categories not found' => '',
    'Change Password' => '',
    'Contact Support' => '',
    'Contact us' => '',
    'Create Rule' => '',
    'Forgot your password?' => '',
    'Go to Home' => '',
    'Last updated on {updated_at} by {updater}' => '',
    'No records' => '',
    'Oops! There was an error...' => '',
    'Posted by {author} on {published_at}' => '',
    'Read More ' => '',
    'Resend activation email' => '',
    'Resend my activation email' => '',
    'Rules' => '',
    'Send Email' => '',
    'Sign up' => '',
    'Sorry, we are unable to resend activation link for email provided.' => '',
    'This email has already been taken.' => '',
    'Your account has been successfully activated.' => '',
    'Your account has been successfully created. Check your email for further instructions.' => '',
    'Your activation link: {url}' => '',
    'by {author} on {published_at} in {category}' => '',
];
