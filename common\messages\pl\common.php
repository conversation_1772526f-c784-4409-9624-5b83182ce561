<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message/extract' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return [
    '"{attribute}" must be a valid JSON' => '"{attribute}" musi być poprawnym dokumentem JSON',
    'API access token' => 'Access token API',
    'Active' => 'Aktywny',
    'Article ID' => 'ID Artykułu',
    'Article View' => 'Widok artykułu',
    'Author' => 'Autor',
    'Base URL' => 'Base Url',
    'Base Url' => 'Base Url',
    'Body' => 'Treść',
    'Caption' => 'Nagłówek',
    'Carousel ID' => 'ID Karuzeli',
    'Category' => 'Kategoria',
    'Comment' => 'Komentarz',
    'Component' => 'Komponent',
    'Config' => 'Konfiguracja',
    'Created At' => 'Data utworzenia',
    'Created at' => 'Data utworzenia',
    'Deleted' => 'Usunięty',
    'Down to maintenance.' => 'Strona w konserwacji.',
    'E-mail' => 'E-mail',
    'Email' => 'Email',
    'Expire At' => 'Wygasa dnia',
    'File Type' => 'Typ pliku',
    'Firstname' => 'Imię',
    'Gender' => 'Płeć',
    'ID' => 'ID',
    'Image' => 'Obraz',
    'Key' => 'Klucz',
    'Last login' => 'Data ostatniego logowania',
    'Lastname' => 'Nazwisko',
    'Locale' => 'Ustawienia lokalne',
    'Middlename' => 'Drugie imię',
    'Name' => 'Nazwa',
    'Not Active' => 'Nieaktywny',
    'Order' => 'Kolejność',
    'Page View' => 'Widok strony',
    'Parent Category' => 'Kategoria nadrzędna',
    'Password' => 'Hasło',
    'Path' => 'Ścieżka',
    'Picture' => 'Obraz',
    'Published' => 'Opublikowany',
    'Published At' => 'Data publikacji',
    'Roles' => 'Role',
    'Size' => 'Wielkość',
    'Slug' => 'Slug',
    'Status' => 'Status',
    'Thumbnail' => 'Miniaturka',
    'Title' => 'Tytuł',
    'Token' => 'Token',
    'Type' => 'Typ',
    'Updated At' => 'Data aktualizacji',
    'Updated at' => 'Data aktualizacji',
    'Updater' => 'Zmieniony przez',
    'Upload Ip' => 'IP',
    'Url' => 'Adres Url',
    'User ID' => 'ID Użytkownika',
    'Username' => 'Nazwa użytkownika',
    'Value' => 'Wartość',
    'Draft' => '',
];
