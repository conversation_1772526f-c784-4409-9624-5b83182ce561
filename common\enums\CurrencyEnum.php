<?php

namespace common\enums;

interface CurrencyEnum
{
    const CURRENCY_CODE_RUB = 'RUB';
    const CURRENCY_CODE_UZS = 'UZS';
    const CURRENCY_CODE_EURO = 'EURO';
    const CURRENCY_CODE_USD = 'USD';
    const CURRENCY_CODE_LIST = [
        self::CURRENCY_CODE_UZS,
        self::CURRENCY_CODE_USD,
        self::CURRENCY_CODE_RUB,
        self::CURRENCY_CODE_EURO,
    ];

    const CURRENCY_CODE_SIGN_LIST = [
        self::CURRENCY_CODE_UZS => 'so`m',
        self::CURRENCY_CODE_USD => '$',
        self::CURRENCY_CODE_RUB => '₽',
        self::CURRENCY_CODE_EURO => '€',
    ];
}