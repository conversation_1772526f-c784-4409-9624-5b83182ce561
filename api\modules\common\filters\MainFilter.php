<?php


namespace api\modules\common\filters;


use api\modules\common\resources\MainResource;
use common\base\BaseModel;
use yii\db\Query;
use Yii;

class MainFilter extends BaseModel
{
    public function getResult()
    {
        // Determine the name field based on current language
        $language = Yii::$app->language;
        $nameField = 'name_uz'; // default fallback

        // Map language codes to database field names
        switch ($language) {
            case 'uz':
            case 'uz-UZ':
                $nameField = 'name_uz';
                break;
            case 'ru':
            case 'ru-RU':
                $nameField = 'name_ru';
                break;
            case 'en':
            case 'en-US':
                $nameField = 'name_en';
                break;
            case 'uzk':
                $nameField = 'name_uzk';
                break;
            default:
                $nameField = 'name_uz'; // fallback to Uzbek
                break;
        }

        $query = MainResource::find()
            ->select(['js.id', "js.{$nameField} as name", 'js.icon_id' , 'COUNT(v.category_type_id) as vacancy_count'])
            ->from('job_specification js')
            ->leftJoin('vacancy v', 'v.category_type_id = js.id')
            ->groupBy('js.id');

        return paginate($query->orderBy(['js.id' => SORT_ASC]));
    }
}