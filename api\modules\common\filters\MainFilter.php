<?php


namespace api\modules\common\filters;


use api\modules\common\resources\MainResource;
use common\base\BaseModel;
use yii\db\Query;

class MainFilter extends BaseModel
{
    public function getResult()
    {
        $query = MainResource::find()
            ->select(['js.id', 'js.name_uz as name_uz', 'js.icon_id' , 'COUNT(v.category_type_id) as vacancy_count'])
            ->from('job_specification js')
            ->leftJoin('vacancy v', 'v.category_type_id = js.id')
            ->groupBy('js.id');

        return paginate($query->orderBy(['js.id' => SORT_ASC]));
    }
}