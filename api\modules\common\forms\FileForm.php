<?php

namespace api\modules\common\forms;

use Yii;
use Exception;
use common\enums\FileEnum;
use common\base\BaseModel;
use api\modules\common\resources\FileResource;
use common\enums\StatusEnum;
use yii\helpers\FileHelper;
use yii\web\UploadedFile;

class FileForm extends BaseModel
{
    public ?string $title = null;
    public ?UploadedFile $file = null;
    public function __construct(
        public FileResource $model,
        public ?string $type = FileEnum::FILE_TYPE_DOCUMENT,
        $config = []
    )
    {
        parent::__construct($config);
    }

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [['file'], 'file', 'extensions' => $this->type === FileEnum::FILE_TYPE_DOCUMENT ? ['pdf', 'docx', 'doc'] : ['svg', 'jpg', 'jpeg', 'png']],
            [['file'], 'file', 'maxSize' => 2 * 1024 * 1024,],
            ['title', 'string']
        ];
    }

    /**
     * @throws Exception
     */
    public function getResult(): bool|int
    {
        /** @var UploadedFile $file */
        $file = $this->file = UploadedFile::getInstanceByName('file');
        if (!$file instanceof UploadedFile)
        {
            $this->addError('*','Файл не загружен');
            return false;
        }
        if (!$this->validate())
            return false;

        try {
            $currentDate = date('Y-m-d');
            $folderPath = Yii::getAlias('@uploads') . '/company/' . $currentDate;
            if (!is_dir($folderPath))
                FileHelper::createDirectory($folderPath,0777);

            $_filename = '/' . $file->baseName . '_' . (int)microtime(true) . '.' . $file->extension;
            $file->saveAs($folderPath . $_filename);

            $this->model->path = $_filename;
            $this->model->day = $currentDate;
            $this->model->title = $this->title ?? $file->baseName;
            $this->model->size = $file->size;
            $this->model->type = $file->extension;

            $this->model->status = StatusEnum::STATUS_ACTIVE;
            if (!$this->model->save()) {
                @unlink($folderPath . $_filename);
                $this->addErrors($this->model->errors);
                return false;
            }
            return $this->model->id;
        } catch (\Throwable $e) {
            $this->addError('*', $e->getMessage());
            return false;
        }
    }
}