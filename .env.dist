# Framework
# ---------
YII_DEBUG=true
YII_ENV=dev
APP_MAINTENANCE=0

# Application
# -----------
LINK_ASSETS=true

# Databases
# ---------
DB_DSN=pgsql:host=db;port=5432;dbname=test_db
DB_USERNAME=test_user
DB_PASSWORD=test_user
DB_TABLE_PREFIX=

TEST_DB_DSN=pgsql:host=db;port=5432;dbname=test_db
TEST_DB_USERNAME=postgres
TEST_DB_PASSWORD=postgres
TEST_DB_TABLE_PREFIX=

# Urls
# ----
API_HOST_INFO=http://api.yii2-starter-kit.localhost
FRONTEND_HOST_INFO=http://yii2-starter-kit.localhost
BACKEND_HOST_INFO=http://backend.yii2-starter-kit.localhost
STORAGE_HOST_INFO=http://storage.yii2-starter-kit.localhost

# Single domain example
# ----
#FRONTEND_HOST_INFO=http://yii2-starter-kit.localhost
#FRONTEND_BASE_URL=/
#BACKEND_HOST_INFO=http://yii2-starter-kit.localhost
#BACKEND_BASE_URL=/backend/web
#STORAGE_HOST_INFO=http://yii2-starter-kit.localhost
#STORAGE_BASE_URL=/storage/web


# Other
# -----
SMTP_HOST=mailcatcher
SMTP_PORT=1025

FRONTEND_COOKIE_VALIDATION_KEY=<generated_key>
BACKEND_COOKIE_VALIDATION_KEY=<generated_key>

ADMIN_EMAIL=<EMAIL>
ROBOT_EMAIL=<EMAIL>

GITHUB_CLIENT_ID=your-client-id
GITHUB_CLIENT_SECRET=your-client-secret

GLIDE_SIGN_KEY=<generated_key>
GLIDE_MAX_IMAGE_SIZE=4000000

# To resolve "Invalid volumes",For Toolbox. (https://github.com/docker/toolbox/issues/607)
COMPOSE_CONVERT_WINDOWS_PATHS=1

# Config for Docker for Mac
#XDEBUG_CONFIG = "remote_connect_back=0 remote_host=host.docker.internal"

# Telegram channel, bots and etc.
TELEGRAM_BOT_TOKEN=**********************************************
TELEGRAM_CONTACT_GROUP_ID=-1002758503392
