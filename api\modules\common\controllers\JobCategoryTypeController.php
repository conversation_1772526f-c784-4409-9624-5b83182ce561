<?php

namespace api\modules\common\controllers;

use api\modules\common\forms\JobSpecificationDeleteForm;
use Yii;
use api\modules\common\filters\JobSpecificationFilter;
use api\modules\common\forms\JobSpecificationUpdateForm;
use api\modules\common\resources\JobSpecificationResource;
use common\enums\JobSpecificationEnum;
use api\modules\common\forms\JobSpecificationForm;
use common\base\BaseController;
use yii\web\NotFoundHttpException;

class JobCategoryTypeController extends BaseController
{
    protected ?int $type = JobSpecificationEnum::SPECIFICATION_CATEGORY_TYPE;

    public function actionIndex(): array
    {
        return $this->sendResponse(
            new JobSpecificationFilter($this->type),
            Yii::$app->request->queryParams
        );
    }

    /**
     * @throws NotFoundHttpException
     */
    public function actionView($id): array
    {
        return $this->sendModel($this->findOne($id));
    }
    public function actionCreate(): array
    {
        return $this->sendResponse(
            new JobSpecificationForm(
                $this->type,
                new JobSpecificationResource(),
            ),
            Yii::$app->request->bodyParams
        );
    }

    /**
     * @throws NotFoundHttpException
     */
    public function actionUpdate($id): array
    {
        return $this->sendResponse(
            new JobSpecificationUpdateForm(
                $this->type,
                $this->findOne($id)
            ),
            Yii::$app->request->bodyParams
        );
    }

    /**
     * @throws NotFoundHttpException
     */
    public function actionDelete($id): array
    {
        return $this->sendResponse(
            new JobSpecificationDeleteForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }


    /**
     * @throws NotFoundHttpException
     */
    protected function findOne($id): JobSpecificationResource
    {
        $model = JobSpecificationResource::findOne(['id' => $id,'deleted_at' => null,'type' => $this->type]);
        if (!$model) {
            throw new NotFoundHttpException('Model does not exist.');
        }
        return $model;
    }
}