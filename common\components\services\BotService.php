<?php

namespace common\components\services;

use Yii;
use yii\base\Component;
use yii\base\InvalidConfigException;
use yii\httpclient\Client;

class BotService extends Component
{
    const PARSE_MODE_MARKDOWN = 'Markdown';
    const PARSE_MODE_MARKDOWN_V2 = 'MarkdownV2';
    const PARSE_MODE_HTML = 'HTML';

    /**
     * @var string|null Telegram Bot API token
     */
    public ?string $botToken = null;

    /**
     * @var string|int|null Telegram chat ID for sending messages
     */
    public string|int|null $chatID = null;

    /**
     * @var string|null Base URL for Telegram Bot API
     */
    public ?string $apiUrl = 'https://api.telegram.org/bot';

    /**
     * @var Client HTTP client for making API requests
     */
    private Client $client;

    /**
     * @var string|null Message to send
     */
    public ?string $message = null;

    private ?string $method = null;
    private ?string $parseMode = self::PARSE_MODE_HTML;

    /**
     * Set telegram bot token
     * @param $token string|null
     * @return $this
     */
    public function setToken(string $token = null): static
    {
        $this->botToken = $token ?: env('TELEGRAM_BOT_TOKEN');
        return $this;
    }

    /**
     * Set client to send request telegram server
     * @param $client Client|null
     * @return $this
     */
    public function setClient(Client $client = null): static
    {
        $this->client = $client ?: new Client(['baseUrl' => $this->apiUrl . $this->botToken]);
        return $this;
    }

    /**
     * Set chat id
     * @param $chatID
     * @return $this
     */
    public function setChatID($chatID): static
    {
        $this->chatID = $chatID;
        return $this;
    }

    /**
     * Set request method
     * @param string $method
     * @return $this
     */
    public function setMethod(string $method = 'sendMessage'): static
    {
        $this->method = $method;
        return $this;
    }

    /**
     * Set request method
     * @param string $mode
     * @return $this
     */
    public function setParseMode(string $mode): static
    {
        $this->parseMode = $mode;
        return $this;
    }

    /**
     * Set request method
     * @param string $message
     * @return $this
     */
    public function setMessage(string $message): static
    {
        $this->message = $message;
        return $this;
    }

    public function __construct($config = [])
    {
        parent::__construct($config);
    }

    /**
     * Initialize the component
     * @throws InvalidConfigException
     */
    public function init(): void
    {
        parent::init();
        $this->setToken();
        $this->setClient();
        $this->setMethod();
        if (empty($this->botToken)) {
            throw new InvalidConfigException('botToken must be configured.');
        }
    }

    /**
     * Send a message to the configured Telegram chat
     * @return bool Whether the message was sent successfully
     */
    public function sendMessage(): mixed
    {
        try {
            if (empty($this->chatID)) {
                throw new InvalidConfigException('chatID must be configured.');
            }
            $response = $this->client->post('/' . $this->method, [
                'chat_id' => $this->chatID,
                'text' => $this->message,
                'parse_mode' => $this->parseMode,
            ])->send();
            if ($response->isOk) {
                return true;
            } else {
                Yii::error('Failed to send Telegram message: ' . $response->content);
                return false;
            }
        } catch (\Throwable $e) {
            Yii::error('Error sending Telegram message: ' . $e->getMessage());
            return false;
        }
    }
}