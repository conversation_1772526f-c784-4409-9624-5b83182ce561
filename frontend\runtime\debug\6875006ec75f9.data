a:14:{s:6:"config";s:8708:"a:5:{s:10:"phpVersion";s:6:"8.0.30";s:10:"yiiVersion";s:6:"2.0.46";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.46";s:4:"name";s:16:"Yii2 Starter Kit";s:7:"version";s:3:"1.0";s:8:"language";s:5:"ru-RU";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";s:4:"true";}s:3:"php";a:5:{s:7:"version";s:6:"8.0.30";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:37:{s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:7:"2.0.7.0";s:5:"alias";a:1:{s:8:"@yii/jui";s:28:"/app/vendor/yiisoft/yii2-jui";}}s:22:"alexantr/yii2-elfinder";a:3:{s:4:"name";s:22:"alexantr/yii2-elfinder";s:7:"version";s:7:"1.8.0.0";s:5:"alias";a:1:{s:18:"@alexantr/elfinder";s:38:"/app/vendor/alexantr/yii2-elfinder/src";}}s:30:"asofter/yii2-imperavi-redactor";a:3:{s:4:"name";s:30:"asofter/yii2-imperavi-redactor";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:13:"@yii/imperavi";s:55:"/app/vendor/asofter/yii2-imperavi-redactor/yii/imperavi";}}s:30:"kartik-v/yii2-widget-typeahead";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-typeahead";s:7:"version";s:7:"1.0.4.0";s:5:"alias";a:1:{s:17:"@kartik/typeahead";s:46:"/app/vendor/kartik-v/yii2-widget-typeahead/src";}}s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:7:"3.0.5.0";s:5:"alias";a:1:{s:12:"@kartik/base";s:41:"/app/vendor/kartik-v/yii2-krajee-base/src";}}s:30:"kartik-v/yii2-widget-touchspin";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-touchspin";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:17:"@kartik/touchspin";s:46:"/app/vendor/kartik-v/yii2-widget-touchspin/src";}}s:31:"kartik-v/yii2-widget-timepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-timepicker";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:12:"@kartik/time";s:47:"/app/vendor/kartik-v/yii2-widget-timepicker/src";}}s:32:"kartik-v/yii2-widget-switchinput";a:3:{s:4:"name";s:32:"kartik-v/yii2-widget-switchinput";s:7:"version";s:7:"1.3.1.0";s:5:"alias";a:1:{s:19:"@kartik/switchinput";s:44:"/app/vendor/kartik-v/yii2-widget-switchinput";}}s:28:"kartik-v/yii2-widget-spinner";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-spinner";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/spinner";s:44:"/app/vendor/kartik-v/yii2-widget-spinner/src";}}s:28:"kartik-v/yii2-widget-sidenav";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-sidenav";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/sidenav";s:40:"/app/vendor/kartik-v/yii2-widget-sidenav";}}s:27:"kartik-v/yii2-widget-rating";a:3:{s:4:"name";s:27:"kartik-v/yii2-widget-rating";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:14:"@kartik/rating";s:43:"/app/vendor/kartik-v/yii2-widget-rating/src";}}s:31:"kartik-v/yii2-widget-rangeinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-rangeinput";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:13:"@kartik/range";s:47:"/app/vendor/kartik-v/yii2-widget-rangeinput/src";}}s:26:"kartik-v/yii2-widget-growl";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-growl";s:7:"version";s:7:"1.1.2.0";s:5:"alias";a:1:{s:13:"@kartik/growl";s:42:"/app/vendor/kartik-v/yii2-widget-growl/src";}}s:28:"kartik-v/yii2-widget-select2";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-select2";s:7:"version";s:7:"2.2.4.0";s:5:"alias";a:1:{s:15:"@kartik/select2";s:44:"/app/vendor/kartik-v/yii2-widget-select2/src";}}s:28:"kartik-v/yii2-widget-depdrop";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-depdrop";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:15:"@kartik/depdrop";s:44:"/app/vendor/kartik-v/yii2-widget-depdrop/src";}}s:30:"kartik-v/yii2-widget-fileinput";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-fileinput";s:7:"version";s:7:"1.1.1.0";s:5:"alias";a:1:{s:12:"@kartik/file";s:46:"/app/vendor/kartik-v/yii2-widget-fileinput/src";}}s:26:"kartik-v/yii2-widget-alert";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-alert";s:7:"version";s:7:"1.1.5.0";s:5:"alias";a:1:{s:13:"@kartik/alert";s:42:"/app/vendor/kartik-v/yii2-widget-alert/src";}}s:31:"kartik-v/yii2-widget-colorinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-colorinput";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:13:"@kartik/color";s:47:"/app/vendor/kartik-v/yii2-widget-colorinput/src";}}s:26:"kartik-v/yii2-widget-affix";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-affix";s:7:"version";s:7:"1.0.0.0";s:5:"alias";a:1:{s:13:"@kartik/affix";s:38:"/app/vendor/kartik-v/yii2-widget-affix";}}s:31:"kartik-v/yii2-widget-activeform";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-activeform";s:7:"version";s:7:"1.6.2.0";s:5:"alias";a:1:{s:12:"@kartik/form";s:47:"/app/vendor/kartik-v/yii2-widget-activeform/src";}}s:21:"kartik-v/yii2-widgets";a:3:{s:4:"name";s:21:"kartik-v/yii2-widgets";s:7:"version";s:7:"3.4.1.0";s:5:"alias";a:1:{s:15:"@kartik/widgets";s:37:"/app/vendor/kartik-v/yii2-widgets/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:7:"1.5.1.0";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:51:"/app/vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:31:"kartik-v/yii2-widget-datepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-datepicker";s:7:"version";s:7:"1.4.8.0";s:5:"alias";a:1:{s:12:"@kartik/date";s:47:"/app/vendor/kartik-v/yii2-widget-datepicker/src";}}s:20:"trntv/yii2-aceeditor";a:3:{s:4:"name";s:20:"trntv/yii2-aceeditor";s:7:"version";s:7:"2.1.2.0";s:5:"alias";a:1:{s:16:"@trntv/aceeditor";s:36:"/app/vendor/trntv/yii2-aceeditor/src";}}s:22:"trntv/yii2-command-bus";a:3:{s:4:"name";s:22:"trntv/yii2-command-bus";s:7:"version";s:7:"3.2.0.0";s:5:"alias";a:1:{s:10:"@trntv/bus";s:38:"/app/vendor/trntv/yii2-command-bus/src";}}s:18:"yiisoft/yii2-queue";a:3:{s:4:"name";s:18:"yiisoft/yii2-queue";s:7:"version";s:7:"2.3.4.0";s:5:"alias";a:11:{s:10:"@yii/queue";s:34:"/app/vendor/yiisoft/yii2-queue/src";s:13:"@yii/queue/db";s:45:"/app/vendor/yiisoft/yii2-queue/src/drivers/db";s:14:"@yii/queue/sqs";s:46:"/app/vendor/yiisoft/yii2-queue/src/drivers/sqs";s:15:"@yii/queue/amqp";s:47:"/app/vendor/yiisoft/yii2-queue/src/drivers/amqp";s:15:"@yii/queue/file";s:47:"/app/vendor/yiisoft/yii2-queue/src/drivers/file";s:15:"@yii/queue/sync";s:47:"/app/vendor/yiisoft/yii2-queue/src/drivers/sync";s:16:"@yii/queue/redis";s:48:"/app/vendor/yiisoft/yii2-queue/src/drivers/redis";s:16:"@yii/queue/stomp";s:48:"/app/vendor/yiisoft/yii2-queue/src/drivers/stomp";s:18:"@yii/queue/gearman";s:50:"/app/vendor/yiisoft/yii2-queue/src/drivers/gearman";s:20:"@yii/queue/beanstalk";s:52:"/app/vendor/yiisoft/yii2-queue/src/drivers/beanstalk";s:23:"@yii/queue/amqp_interop";s:55:"/app/vendor/yiisoft/yii2-queue/src/drivers/amqp_interop";}}s:26:"trntv/yii2-datetime-widget";a:3:{s:4:"name";s:26:"trntv/yii2-datetime-widget";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:19:"@trntv/yii/datetime";s:42:"/app/vendor/trntv/yii2-datetime-widget/src";}}s:16:"trntv/yii2-glide";a:3:{s:4:"name";s:16:"trntv/yii2-glide";s:7:"version";s:7:"1.2.2.0";s:5:"alias";a:1:{s:12:"@trntv/glide";s:32:"/app/vendor/trntv/yii2-glide/src";}}s:24:"rmrevin/yii2-fontawesome";a:3:{s:4:"name";s:24:"rmrevin/yii2-fontawesome";s:7:"version";s:7:"3.7.0.0";s:5:"alias";a:1:{s:24:"@rmrevin/yii/fontawesome";s:36:"/app/vendor/rmrevin/yii2-fontawesome";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"2.0.14.0";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:39:"/app/vendor/yiisoft/yii2-httpclient/src";}}s:30:"yii2-starter-kit/yii2-file-kit";a:3:{s:4:"name";s:30:"yii2-starter-kit/yii2-file-kit";s:7:"version";s:7:"2.1.5.0";s:5:"alias";a:1:{s:14:"@trntv/filekit";s:46:"/app/vendor/yii2-starter-kit/yii2-file-kit/src";}}s:23:"yiisoft/yii2-authclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-authclient";s:7:"version";s:8:"2.2.13.0";s:5:"alias";a:1:{s:15:"@yii/authclient";s:39:"/app/vendor/yiisoft/yii2-authclient/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.10.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:39:"/app/vendor/yiisoft/yii2-bootstrap4/src";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/faker";s:34:"/app/vendor/yiisoft/yii2-faker/src";}}s:24:"yiisoft/yii2-swiftmailer";a:3:{s:4:"name";s:24:"yiisoft/yii2-swiftmailer";s:7:"version";s:7:"2.1.3.0";s:5:"alias";a:1:{s:16:"@yii/swiftmailer";s:40:"/app/vendor/yiisoft/yii2-swiftmailer/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.5.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:32:"/app/vendor/yiisoft/yii2-gii/src";}}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.21.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:34:"/app/vendor/yiisoft/yii2-debug/src";}}}}";s:3:"log";s:8122:"a:1:{s:8:"messages";a:14:{i:0;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752498286.642073;i:4;a:0:{}i:5;i:1149296;}i:1;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1752498286.64209;i:4;a:0:{}i:5;i:1150472;}i:2;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752498286.650133;i:4;a:0:{}i:5;i:1156032;}i:3;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1752498286.666628;i:4;a:0:{}i:5;i:1254160;}i:4;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:1752498286.780188;i:4;a:0:{}i:5;i:1424816;}i:5;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752498286.811401;i:4;a:0:{}i:5;i:1448112;}i:6;a:6:{i:0;s:69:"Bootstrap with common\components\maintenance\Maintenance::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752498286.821414;i:4;a:0:{}i:5;i:1530224;}i:7;a:6:{i:0;s:65:"Opening DB connection: pgsql:host=db;port=5432;dbname=skillhub_db";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1752498286.932016;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:71;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:113;s:8:"function";s:8:"getModel";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:28:"/app/frontend/config/web.php";s:4:"line";i:46;s:8:"function";s:3:"get";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}}i:5;i:1806408;}i:10;a:6:{i:0;s:67:"SELECT * FROM "key_storage_item" WHERE "key"='frontend.maintenance'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752498286.938857;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:71;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:113;s:8:"function";s:8:"getModel";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:28:"/app/frontend/config/web.php";s:4:"line";i:46;s:8:"function";s:3:"get";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}}i:5;i:1810432;}i:13;a:6:{i:0;s:2822:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'key_storage_item'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752498286.947305;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:71;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:113;s:8:"function";s:8:"getModel";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:28:"/app/frontend/config/web.php";s:4:"line";i:46;s:8:"function";s:3:"get";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}}i:5;i:1833416;}i:16;a:6:{i:0;s:884:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='key_storage_item'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752498286.959805;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:71;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:113;s:8:"function";s:8:"getModel";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:28:"/app/frontend/config/web.php";s:4:"line";i:46;s:8:"function";s:3:"get";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}}i:5;i:1849064;}i:25;a:6:{i:0;s:37:"Route requested: 'gii/default/action'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:1752498286.971103;i:4;a:0:{}i:5;i:1931608;}i:26;a:6:{i:0;s:32:"Route to run: gii/default/action";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:1752498287.000954;i:4;a:0:{}i:5;i:1949776;}i:27;a:6:{i:0;s:69:"Running action: yii\gii\controllers\DefaultController::actionAction()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:1752498287.130805;i:4;a:0:{}i:5;i:2127544;}}}";s:9:"profiling";s:13118:"a:3:{s:6:"memory";i:2250336;s:4:"time";d:0.6528220176696777;s:8:"messages";a:8:{i:8;a:6:{i:0;s:65:"Opening DB connection: pgsql:host=db;port=5432;dbname=skillhub_db";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1752498286.932033;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:71;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:113;s:8:"function";s:8:"getModel";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:28:"/app/frontend/config/web.php";s:4:"line";i:46;s:8:"function";s:3:"get";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}}i:5;i:1808288;}i:9;a:6:{i:0;s:65:"Opening DB connection: pgsql:host=db;port=5432;dbname=skillhub_db";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1752498286.938826;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:71;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:113;s:8:"function";s:8:"getModel";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:28:"/app/frontend/config/web.php";s:4:"line";i:46;s:8:"function";s:3:"get";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}}i:5;i:1811352;}i:11;a:6:{i:0;s:67:"SELECT * FROM "key_storage_item" WHERE "key"='frontend.maintenance'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752498286.938881;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:71;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:113;s:8:"function";s:8:"getModel";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:28:"/app/frontend/config/web.php";s:4:"line";i:46;s:8:"function";s:3:"get";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}}i:5;i:1813752;}i:12;a:6:{i:0;s:67:"SELECT * FROM "key_storage_item" WHERE "key"='frontend.maintenance'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752498286.940111;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:71;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:113;s:8:"function";s:8:"getModel";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:28:"/app/frontend/config/web.php";s:4:"line";i:46;s:8:"function";s:3:"get";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}}i:5;i:1816552;}i:14;a:6:{i:0;s:2822:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'key_storage_item'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752498286.947357;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:71;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:113;s:8:"function";s:8:"getModel";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:28:"/app/frontend/config/web.php";s:4:"line";i:46;s:8:"function";s:3:"get";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}}i:5;i:1844872;}i:15;a:6:{i:0;s:2822:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'key_storage_item'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752498286.954909;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:71;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:113;s:8:"function";s:8:"getModel";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:28:"/app/frontend/config/web.php";s:4:"line";i:46;s:8:"function";s:3:"get";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}}i:5;i:1856344;}i:17;a:6:{i:0;s:884:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='key_storage_item'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752498286.959836;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:71;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:113;s:8:"function";s:8:"getModel";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:28:"/app/frontend/config/web.php";s:4:"line";i:46;s:8:"function";s:3:"get";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}}i:5;i:1854760;}i:18;a:6:{i:0;s:884:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='key_storage_item'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752498286.962406;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:71;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:113;s:8:"function";s:8:"getModel";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:28:"/app/frontend/config/web.php";s:4:"line";i:46;s:8:"function";s:3:"get";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}}i:5;i:1857080;}}}";s:2:"db";s:11593:"a:1:{s:8:"messages";a:6:{i:11;a:6:{i:0;s:67:"SELECT * FROM "key_storage_item" WHERE "key"='frontend.maintenance'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752498286.938881;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:71;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:113;s:8:"function";s:8:"getModel";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:28:"/app/frontend/config/web.php";s:4:"line";i:46;s:8:"function";s:3:"get";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}}i:5;i:1813752;}i:12;a:6:{i:0;s:67:"SELECT * FROM "key_storage_item" WHERE "key"='frontend.maintenance'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752498286.940111;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:71;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:113;s:8:"function";s:8:"getModel";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:28:"/app/frontend/config/web.php";s:4:"line";i:46;s:8:"function";s:3:"get";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}}i:5;i:1816552;}i:14;a:6:{i:0;s:2822:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'key_storage_item'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752498286.947357;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:71;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:113;s:8:"function";s:8:"getModel";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:28:"/app/frontend/config/web.php";s:4:"line";i:46;s:8:"function";s:3:"get";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}}i:5;i:1844872;}i:15;a:6:{i:0;s:2822:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'key_storage_item'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752498286.954909;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:71;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:113;s:8:"function";s:8:"getModel";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:28:"/app/frontend/config/web.php";s:4:"line";i:46;s:8:"function";s:3:"get";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}}i:5;i:1856344;}i:17;a:6:{i:0;s:884:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='key_storage_item'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752498286.959836;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:71;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:113;s:8:"function";s:8:"getModel";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:28:"/app/frontend/config/web.php";s:4:"line";i:46;s:8:"function";s:3:"get";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}}i:5;i:1854760;}i:18;a:6:{i:0;s:884:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='key_storage_item'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752498286.962406;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:71;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:113;s:8:"function";s:8:"getModel";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:28:"/app/frontend/config/web.php";s:4:"line";i:46;s:8:"function";s:3:"get";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}}i:5;i:1857080;}}}";s:5:"event";s:2668:"a:15:{i:0;a:5:{s:4:"time";d:1752498286.886218;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:1;a:5:{s:4:"time";d:1752498286.938816;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:2;a:5:{s:4:"time";d:1752498286.944773;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\models\KeyStorageItem";}i:3;a:5:{s:4:"time";d:1752498286.962672;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\models\KeyStorageItem";}i:4;a:5:{s:4:"time";d:1752498286.971024;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:5;a:5:{s:4:"time";d:1752498287.0033;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:6;a:5:{s:4:"time";d:1752498287.003312;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:14:"yii\gii\Module";}i:7;a:5:{s:4:"time";d:1752498287.130731;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"yii\gii\controllers\DefaultController";}i:8;a:5:{s:4:"time";d:1752498287.181904;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"yii\gii\controllers\DefaultController";}i:9;a:5:{s:4:"time";d:1752498287.181915;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:14:"yii\gii\Module";}i:10;a:5:{s:4:"time";d:1752498287.181919;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:11;a:5:{s:4:"time";d:1752498287.181927;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:12;a:5:{s:4:"time";d:1752498287.181932;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:13;a:5:{s:4:"time";d:1752498287.191607;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:14;a:5:{s:4:"time";d:1752498287.19166;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:91:"a:3:{s:5:"start";d:1752498286.544167;s:3:"end";d:1752498287.197032;s:6:"memory";i:2250336;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:1287:"a:3:{s:8:"messages";a:6:{i:19;a:6:{i:0;a:3:{s:4:"rule";s:5:"debug";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752498286.971068;i:4;a:0:{}i:5;i:1927808;}i:20;a:6:{i:0;a:3:{s:4:"rule";s:43:"debug/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752498286.971079;i:4;a:0:{}i:5;i:1928560;}i:21;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752498286.971082;i:4;a:0:{}i:5;i:1929312;}i:22;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752498286.971085;i:4;a:0:{}i:5;i:1930064;}i:23;a:6:{i:0;s:71:"Request parsed with URL rule: gii/<controller:[\w\-]+>/<action:[\w\-]+>";i:1;i:8;i:2;s:29:"yii\web\UrlRule::parseRequest";i:3;d:1752498286.971095;i:4;a:0:{}i:5;i:1932120;}i:24;a:6:{i:0;a:3:{s:4:"rule";s:41:"gii/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:1;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752498286.971098;i:4;a:0:{}i:5;i:1931976;}}s:5:"route";s:18:"gii/default/action";s:6:"action";s:53:"yii\gii\controllers\DefaultController::actionAction()";}";s:7:"request";s:9479:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:13:{s:6:"cookie";s:216:"PHPSESSID=47f8d75ca172651717bb4ea61deed986; _csrf=c56cd475f638988a5294c4a29706aea7242e6a2fb6fcbb8ff8e50933266fa2d5a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22wJbPLnG5bahlQLkUphtc08pvUaWWuZjk%22%3B%7D";s:15:"accept-language";s:35:"ru-RU,ru;q=0.9,en-US;q=0.8,en;q=0.7";s:15:"accept-encoding";s:13:"gzip, deflate";s:7:"referer";s:30:"http://skill-hub.loc/gii/model";s:6:"origin";s:20:"http://skill-hub.loc";s:12:"content-type";s:48:"application/x-www-form-urlencoded; charset=UTF-8";s:6:"accept";s:3:"*/*";s:10:"user-agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:16:"x-requested-with";s:14:"XMLHttpRequest";s:12:"x-csrf-token";s:88:"VFBdeuBrtos79sBE3ndwErYSRBuFu-yErJzDuckmxLQjGj8qrAXxvlmXqCiPOxtHxnoweLWDnPL5_ZTuvHyu3w==";s:14:"content-length";s:3:"937";s:10:"connection";s:10:"keep-alive";s:4:"host";s:13:"skill-hub.loc";}s:15:"responseHeaders";a:7:{s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:12:"Content-Type";s:24:"text/html; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"6875006ec75f9";s:16:"X-Debug-Duration";s:3:"648";s:12:"X-Debug-Link";s:37:"/debug/default/view?tag=6875006ec75f9";}s:5:"route";s:18:"gii/default/action";s:6:"action";s:53:"yii\gii\controllers\DefaultController::actionAction()";s:12:"actionParams";a:2:{s:2:"id";s:5:"model";s:4:"name";s:17:"GenerateClassName";}s:7:"general";a:5:{s:6:"method";s:4:"POST";s:6:"isAjax";b:1;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:3:{s:12:"Content Type";s:48:"application/x-www-form-urlencoded; charset=UTF-8";s:3:"Raw";s:937:"_csrf=VFBdeuBrtos79sBE3ndwErYSRBuFu-yErJzDuckmxLQjGj8qrAXxvlmXqCiPOxtHxnoweLWDnPL5_ZTuvHyu3w%3D%3D&Generator%5Bdb%5D=db&Generator%5BuseTablePrefix%5D=0&Generator%5BuseSchemaName%5D=0&Generator%5BuseSchemaName%5D=1&Generator%5BtableName%5D=partner&Generator%5BstandardizeCapitals%5D=0&Generator%5Bsingularize%5D=0&Generator%5BmodelClass%5D=&Generator%5Bns%5D=app%5Cmodels&Generator%5BbaseClass%5D=yii%5Cdb%5CActiveRecord&Generator%5BgenerateRelations%5D=all&Generator%5BgenerateJunctionRelationMode%5D=table&Generator%5BgenerateRelationsFromCurrentSchema%5D=0&Generator%5BgenerateRelationsFromCurrentSchema%5D=1&Generator%5BuseClassConstant%5D=0&Generator%5BuseClassConstant%5D=1&Generator%5BgenerateLabelsFromComments%5D=0&Generator%5BgenerateQuery%5D=0&Generator%5BqueryNs%5D=app%5Cmodels&Generator%5BqueryBaseClass%5D=yii%5Cdb%5CActiveQuery&Generator%5BenableI18N%5D=0&Generator%5BmessageCategory%5D=app&Generator%5Btemplate%5D=default";s:17:"Decoded to Params";a:2:{s:5:"_csrf";s:88:"VFBdeuBrtos79sBE3ndwErYSRBuFu-yErJzDuckmxLQjGj8qrAXxvlmXqCiPOxtHxnoweLWDnPL5_ZTuvHyu3w==";s:9:"Generator";a:20:{s:2:"db";s:2:"db";s:14:"useTablePrefix";s:1:"0";s:13:"useSchemaName";s:1:"1";s:9:"tableName";s:7:"partner";s:19:"standardizeCapitals";s:1:"0";s:11:"singularize";s:1:"0";s:10:"modelClass";s:0:"";s:2:"ns";s:10:"app\models";s:9:"baseClass";s:19:"yii\db\ActiveRecord";s:17:"generateRelations";s:3:"all";s:28:"generateJunctionRelationMode";s:5:"table";s:34:"generateRelationsFromCurrentSchema";s:1:"1";s:16:"useClassConstant";s:1:"1";s:26:"generateLabelsFromComments";s:1:"0";s:13:"generateQuery";s:1:"0";s:7:"queryNs";s:10:"app\models";s:14:"queryBaseClass";s:18:"yii\db\ActiveQuery";s:10:"enableI18N";s:1:"0";s:15:"messageCategory";s:3:"app";s:8:"template";s:7:"default";}}}s:6:"SERVER";a:87:{s:17:"PHP_ENABLE_XDEBUG";s:1:"0";s:11:"PHP_USER_ID";s:2:"33";s:8:"HOSTNAME";s:12:"e2c384a79b49";s:11:"PHP_VERSION";s:6:"8.0.30";s:11:"PHP_INI_DIR";s:18:"/usr/local/etc/php";s:8:"GPG_KEYS";s:163:"1729F83938DA44E27BA0F4D3DBDB397470D12172 BFDDD28642824F8118EF77909B67A5C12229118F 2C16C765DBE54A088130F1BC4B9B5F600B55F3B4 39B641343D8C104B2B146DC3F9C39DC0B9698544";s:11:"PHP_LDFLAGS";s:12:"-Wl,-O1 -pie";s:3:"PWD";s:4:"/app";s:4:"HOME";s:8:"/var/www";s:10:"PHP_SHA256";s:64:"216ab305737a5d392107112d618a755dc5df42058226f1670e9db90e77d777d9";s:11:"PHPIZE_DEPS";s:76:"autoconf 		dpkg-dev 		file 		g++ 		gcc 		libc-dev 		make 		pkg-config 		re2c";s:4:"TERM";s:5:"linux";s:7:"PHP_URL";s:51:"https://www.php.net/distributions/php-8.0.30.tar.xz";s:5:"SHLVL";s:1:"0";s:24:"COMPOSER_ALLOW_SUPERUSER";s:1:"1";s:10:"PHP_CFLAGS";s:83:"-fstack-protector-strong -fpic -fpie -O2 -D_LARGEFILE_SOURCE -D_FILE_OFFSET_BITS=64";s:4:"PATH";s:108:"/app:/app/vendor/bin:/root/.composer/vendor/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin";s:11:"PHP_ASC_URL";s:55:"https://www.php.net/distributions/php-8.0.30.tar.xz.asc";s:12:"PHP_CPPFLAGS";s:83:"-fstack-protector-strong -fpic -fpie -O2 -D_LARGEFILE_SOURCE -D_FILE_OFFSET_BITS=64";s:4:"USER";s:8:"www-data";s:11:"HTTP_COOKIE";s:216:"PHPSESSID=47f8d75ca172651717bb4ea61deed986; _csrf=c56cd475f638988a5294c4a29706aea7242e6a2fb6fcbb8ff8e50933266fa2d5a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22wJbPLnG5bahlQLkUphtc08pvUaWWuZjk%22%3B%7D";s:20:"HTTP_ACCEPT_LANGUAGE";s:35:"ru-RU,ru;q=0.9,en-US;q=0.8,en;q=0.7";s:20:"HTTP_ACCEPT_ENCODING";s:13:"gzip, deflate";s:12:"HTTP_REFERER";s:30:"http://skill-hub.loc/gii/model";s:11:"HTTP_ORIGIN";s:20:"http://skill-hub.loc";s:17:"HTTP_CONTENT_TYPE";s:48:"application/x-www-form-urlencoded; charset=UTF-8";s:11:"HTTP_ACCEPT";s:3:"*/*";s:15:"HTTP_USER_AGENT";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:21:"HTTP_X_REQUESTED_WITH";s:14:"XMLHttpRequest";s:17:"HTTP_X_CSRF_TOKEN";s:88:"VFBdeuBrtos79sBE3ndwErYSRBuFu-yErJzDuckmxLQjGj8qrAXxvlmXqCiPOxtHxnoweLWDnPL5_ZTuvHyu3w==";s:19:"HTTP_CONTENT_LENGTH";s:3:"937";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:9:"HTTP_HOST";s:13:"skill-hub.loc";s:15:"REDIRECT_STATUS";s:3:"200";s:11:"SERVER_NAME";s:13:"skill-hub.loc";s:11:"SERVER_PORT";s:2:"80";s:11:"SERVER_ADDR";s:10:"**********";s:11:"REMOTE_PORT";s:5:"50376";s:11:"REMOTE_ADDR";s:10:"**********";s:15:"SERVER_SOFTWARE";s:12:"nginx/1.28.0";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:14:"REQUEST_SCHEME";s:4:"http";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:13:"DOCUMENT_ROOT";s:17:"/app/frontend/web";s:12:"DOCUMENT_URI";s:10:"/index.php";s:11:"REQUEST_URI";s:51:"/gii/default/action?id=model&name=GenerateClassName";s:11:"SCRIPT_NAME";s:10:"/index.php";s:14:"CONTENT_LENGTH";s:3:"937";s:12:"CONTENT_TYPE";s:48:"application/x-www-form-urlencoded; charset=UTF-8";s:14:"REQUEST_METHOD";s:4:"POST";s:12:"QUERY_STRING";s:31:"id=model&name=GenerateClassName";s:15:"SCRIPT_FILENAME";s:27:"/app/frontend/web/index.php";s:9:"FCGI_ROLE";s:9:"RESPONDER";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:1752498285.973918;s:12:"REQUEST_TIME";i:1752498285;s:4:"argv";a:1:{i:0;s:31:"id=model&name=GenerateClassName";}s:4:"argc";i:1;s:9:"YII_DEBUG";s:4:"true";s:7:"YII_ENV";s:3:"dev";s:15:"APP_MAINTENANCE";s:1:"0";s:11:"LINK_ASSETS";s:4:"true";s:6:"DB_DSN";s:42:"pgsql:host=db;port=5432;dbname=skillhub_db";s:11:"DB_USERNAME";s:8:"postgres";s:11:"DB_PASSWORD";s:8:"postgres";s:15:"DB_TABLE_PREFIX";s:0:"";s:11:"TEST_DB_DSN";s:38:"pgsql:host=db;port=5432;dbname=test_db";s:16:"TEST_DB_USERNAME";s:8:"postgres";s:16:"TEST_DB_PASSWORD";s:8:"postgres";s:20:"TEST_DB_TABLE_PREFIX";s:0:"";s:13:"API_HOST_INFO";s:37:"http://api.yii2-starter-kit.localhost";s:18:"FRONTEND_HOST_INFO";s:33:"http://yii2-starter-kit.localhost";s:17:"BACKEND_HOST_INFO";s:41:"http://backend.yii2-starter-kit.localhost";s:17:"STORAGE_HOST_INFO";s:41:"http://storage.yii2-starter-kit.localhost";s:9:"SMTP_HOST";s:11:"mailcatcher";s:9:"SMTP_PORT";s:4:"1025";s:30:"FRONTEND_COOKIE_VALIDATION_KEY";s:15:"<generated_key>";s:29:"BACKEND_COOKIE_VALIDATION_KEY";s:15:"<generated_key>";s:11:"ADMIN_EMAIL";s:32:"<EMAIL>";s:11:"ROBOT_EMAIL";s:32:"<EMAIL>";s:16:"GITHUB_CLIENT_ID";s:14:"your-client-id";s:20:"GITHUB_CLIENT_SECRET";s:18:"your-client-secret";s:14:"GLIDE_SIGN_KEY";s:15:"<generated_key>";s:20:"GLIDE_MAX_IMAGE_SIZE";s:7:"4000000";s:29:"COMPOSE_CONVERT_WINDOWS_PATHS";s:1:"1";s:18:"TELEGRAM_BOT_TOKEN";s:46:"**********************************************";s:25:"TELEGRAM_CONTACT_GROUP_ID";s:14:"-1002758503392";}s:3:"GET";a:2:{s:2:"id";s:5:"model";s:4:"name";s:17:"GenerateClassName";}s:4:"POST";a:2:{s:5:"_csrf";s:88:"VFBdeuBrtos79sBE3ndwErYSRBuFu-yErJzDuckmxLQjGj8qrAXxvlmXqCiPOxtHxnoweLWDnPL5_ZTuvHyu3w==";s:9:"Generator";a:20:{s:2:"db";s:2:"db";s:14:"useTablePrefix";s:1:"0";s:13:"useSchemaName";s:1:"1";s:9:"tableName";s:7:"partner";s:19:"standardizeCapitals";s:1:"0";s:11:"singularize";s:1:"0";s:10:"modelClass";s:0:"";s:2:"ns";s:10:"app\models";s:9:"baseClass";s:19:"yii\db\ActiveRecord";s:17:"generateRelations";s:3:"all";s:28:"generateJunctionRelationMode";s:5:"table";s:34:"generateRelationsFromCurrentSchema";s:1:"1";s:16:"useClassConstant";s:1:"1";s:26:"generateLabelsFromComments";s:1:"0";s:13:"generateQuery";s:1:"0";s:7:"queryNs";s:10:"app\models";s:14:"queryBaseClass";s:18:"yii\db\ActiveQuery";s:10:"enableI18N";s:1:"0";s:15:"messageCategory";s:3:"app";s:8:"template";s:7:"default";}}s:6:"COOKIE";a:2:{s:9:"PHPSESSID";s:32:"47f8d75ca172651717bb4ea61deed986";s:5:"_csrf";s:130:"c56cd475f638988a5294c4a29706aea7242e6a2fb6fcbb8ff8e50933266fa2d5a:2:{i:0;s:5:"_csrf";i:1;s:32:"wJbPLnG5bahlQLkUphtc08pvUaWWuZjk";}";}s:5:"FILES";a:0:{}s:7:"SESSION";a:1:{s:7:"__flash";a:0:{}}}";s:4:"user";s:2:"N;";s:5:"asset";s:6:"a:0:{}";s:7:"summary";a:12:{s:3:"tag";s:13:"6875006ec75f9";s:3:"url";s:71:"http://skill-hub.loc/gii/default/action?id=model&name=GenerateClassName";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:10:"**********";s:4:"time";d:1752498285.973918;s:10:"statusCode";i:200;s:8:"sqlCount";i:3;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:2250336;s:14:"processingTime";d:0.6528220176696777;}s:10:"exceptions";a:0:{}}