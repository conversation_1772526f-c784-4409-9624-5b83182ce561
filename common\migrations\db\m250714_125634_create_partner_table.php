<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%partner}}`.
 */
class m250714_125634_create_partner_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%partner}}', [
            'id' => $this->primaryKey(),
            'file_id' => $this->integer(),
            'title' => $this->string(),
            'created_at' => $this->integer()->unsigned(),
            'updated_at' => $this->integer()->unsigned(),
            'deleted_at' => $this->integer()->unsigned(),
            'created_by' => $this->integer(),
            'updated_by' => $this->integer(),
            'deleted_by' => $this->integer()
        ]);

        $this->createIndex(
            '{{%idx-partner-title}}',
            '{{%partner}}',
            'title'
        );

        $this->addForeignKey(
            '{{%fk-partner-file_id}}',
            '{{%partner}}',
            'file_id',
            '{{%file}}',
            'id',
            'SET NULL',
            'CASCADE'
        );

    }


    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%partner}}');
    }
}
