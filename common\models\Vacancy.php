<?php

namespace common\models;

use common\base\BaseActiveRecord;
use Yii;

/**
 * This is the model class for table "vacancy".
 *
 * @property int $id
 * @property int $company_id
 * @property int|null $category_type_id
 * @property int|null $work_type_id
 * @property string|null $title_uz
 * @property string|null $title_ru
 * @property string|null $title_en
 * @property string|null $title_uzk
 * @property int|null $salary_from
 * @property int|null $salary_to
 * @property int|null $state
 * @property int|null $status
 * @property int|null $experience
 * @property string|null $degree
 * @property int|null $published_at
 * @property string|null $description_uz
 * @property string|null $description_ru
 * @property string|null $description_en
 * @property string|null $description_uzk
 * @property string|null $location
 * @property string|null $latitude
 * @property string|null $longitude
 * @property string|null $currency_code
 * @property int|null $created_at
 * @property int|null $updated_at
 * @property int|null $deleted_at
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property int|null $deleted_by
 *
 * @property JobSpecification $jobCategory
 * @property JobSpecification $jobWork
 */
class Vacancy extends BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName(): string
    {
        return 'vacancy';
    }

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [['category_type_id', 'work_type_id', 'salary_from', 'salary_to', 'state', 'status', 'experience', 'published_at', 'created_at', 'updated_at', 'deleted_at', 'created_by', 'updated_by', 'deleted_by'], 'default', 'value' => null],
            [['company_id', 'category_type_id', 'work_type_id', 'salary_from', 'salary_to', 'state', 'status', 'experience', 'published_at', 'created_at', 'updated_at', 'deleted_at', 'created_by', 'updated_by', 'deleted_by'], 'integer'],
            [['description_uz','description_ru','description_en','description_uzk',], 'string'],
            [['title_uz', 'title_ru', 'title_en', 'title_uzk', 'degree', 'location', 'latitude', 'longitude','currency_code'], 'string', 'max' => 255],
            [['category_type_id'], 'exist', 'skipOnError' => true, 'targetClass' => JobSpecification::class, 'targetAttribute' => ['category_type_id' => 'id']],
            [['work_type_id'], 'exist', 'skipOnError' => true, 'targetClass' => JobSpecification::class, 'targetAttribute' => ['work_type_id' => 'id']],
            [['company_id'], 'exist', 'skipOnError' => true, 'targetClass' => Company::class, 'targetAttribute' => ['company_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels(): array
    {
        return [
            'id' => 'ID',
            'company_id' => 'Company ID',
            'category_type_id' => 'Category Type ID',
            'work_type_id' => 'Work Type ID',
            'title_uz' => 'Title Uz',
            'title_ru' => 'Title Ru',
            'title_en' => 'Title En',
            'title_uzk' => 'Title Uzk',
            'salary_from' => 'Salary From',
            'salary_to' => 'Salary To',
            'state' => 'State',
            'status' => 'Status',
            'experience' => 'Experience',
            'degree' => 'Degree',
            'currency_code' => 'Currency code',
            'published_at' => 'Published At',
            'description_uz' => 'Description Uz',
            'description_ru' => 'Description Ru',
            'description_en' => 'Description En',
            'description_uzk' => 'Description Uzk',
            'location' => 'Location',
            'latitude' => 'Latitude',
            'longitude' => 'Longitude',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'deleted_at' => 'Deleted At',
            'created_by' => 'Created By',
            'updated_by' => 'Updated By',
            'deleted_by' => 'Deleted By',
        ];
    }

    /**
     * Gets query for [[JobCategory]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getJobCategory()
    {
        return $this->hasOne(JobSpecification::class, ['id' => 'category_type_id']);
    }

    /**
     * Gets query for [[JobWork]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getJobWork()
    {
        return $this->hasOne(JobSpecification::class, ['id' => 'work_type_id']);
    }

    public function getCompany()
    {
        return $this->hasOne(Company::class, ['id' => 'company_id']);
    }
}
