<?php

namespace api\modules\common\resources;

use common\models\File;

class FileResource extends File
{
    public function fields(): array
    {
        return [
            'id',
            'title',
            'size',
            'type',
            'path',
            'src',
        ];
    }

    public function getSrc(): string
    {
        return env('API_HOST_INFO', 'https://api.gsxba.uz') . '/uploads/company/' . $this->day .'/'. $this->path;
    }
}