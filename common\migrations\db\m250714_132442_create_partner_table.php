<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%partner}}`.
 */
class m250714_132442_create_partner_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp(): void
    {
        $this->createTable('{{%partner}}', [
            'id' => $this->primaryKey(),
            'title' => $this->string(255),
            'file_id' => $this->integer(),
            'website' => $this->string(255),
            'created_at' => $this->integer()->unsigned(),
            'updated_at' => $this->integer()->unsigned(),
            'deleted_at' => $this->integer()->unsigned(),
            'created_by' => $this->integer(),
            'updated_by' => $this->integer(),
            'deleted_by' => $this->integer(),
        ]);

        // Create index for file_id
        $this->createIndex(
            'idx-partner-file_id',
            '{{%partner}}',
            'file_id'
        );

        // Create foreign key for file_id to file table
        $this->addForeignKey(
            'fk-partner-file_id',
            '{{%partner}}',
            'file_id',
            '{{%file}}',
            'id',
            'SET NULL',
            'CASCADE'
        );
    }


    /**
     * {@inheritdoc}
     */
    public function safeDown(): void
    {
        // Drop foreign key first
        $this->dropForeignKey(
            'fk-partner-file_id',
            '{{%partner}}'
        );

        // Drop index
        $this->dropIndex(
            'idx-partner-file_id',
            '{{%partner}}'
        );

        // Drop table
        $this->dropTable('{{%partner}}');
    }

}
