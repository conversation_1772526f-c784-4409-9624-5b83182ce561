<?php

namespace api\modules\common\forms;

use api\modules\common\resources\FileResource;
use api\modules\common\resources\PartnerResource;
use common\base\BaseModel;
use common\enums\StatusEnum;

class PartnerForm extends BaseModel
{
    public ?string $name_uz = null;
    public ?string $name_ru = null;
    public ?string $name_en = null;
    public ?string $name_uzk = null;
    public ?string $description_uz = null;
    public ?string $description_ru = null;
    public ?string $description_en = null;
    public ?string $description_uzk = null;
    public ?int $logo_id = null;
    public ?string $website = null;
    public ?string $phone = null;
    public ?string $email = null;
    public ?string $address_uz = null;
    public ?string $address_ru = null;
    public ?string $address_en = null;
    public ?string $address_uzk = null;
    public ?int $sort_order = null;
    public ?int $status = null;

    public function __construct(
        public ?PartnerResource $model = null,
        $config = []
    ) {
        parent::__construct($config);
    }

    public function rules(): array
    {
        return [
            [['name_uz'], 'required'],
            [['name_uz', 'name_ru', 'name_en', 'name_uzk'], 'trim'],
            [['name_uz', 'name_ru', 'name_en', 'name_uzk', 'website', 'email', 'address_uz', 'address_ru', 'address_en', 'address_uzk'], 'string', 'max' => 255],
            [['description_uz', 'description_ru', 'description_en', 'description_uzk'], 'string'],
            [['phone'], 'string', 'max' => 20],
            [['logo_id', 'sort_order', 'status'], 'integer'],
            [['email'], 'email'],
            [['website'], 'url'],
            [['sort_order'], 'default', 'value' => 0],
            [['status'], 'default', 'value' => StatusEnum::STATUS_ACTIVE],
            ['status', 'in', 'range' => StatusEnum::STATUS_LIST],
            ['logo_id', 'exist', 'targetClass' => FileResource::class, 'targetAttribute' => ['logo_id' => 'id'], 'filter' => ['deleted_at' => null]],
            [
                'name_uz', 'unique', 'filter' => function ($query) {
                    if (!$this->model->isNewRecord) {
                        $query->andWhere(['<>', 'id', $this->model->id]);
                    }
                    $query->andWhere(['deleted_at' => null]);
                }, 'message' => 'Bu nom allaqachon mavjud.'
            ],
        ];
    }

    public function getResult(): bool
    {
        $this->model->attributes = [
            'name_uz' => $this->name_uz,
            'name_ru' => $this->name_ru,
            'name_en' => $this->name_en,
            'name_uzk' => $this->name_uzk,
            'description_uz' => $this->description_uz,
            'description_ru' => $this->description_ru,
            'description_en' => $this->description_en,
            'description_uzk' => $this->description_uzk,
            'logo_id' => $this->logo_id,
            'website' => $this->website,
            'phone' => $this->phone,
            'email' => $this->email,
            'address_uz' => $this->address_uz,
            'address_ru' => $this->address_ru,
            'address_en' => $this->address_en,
            'address_uzk' => $this->address_uzk,
            'sort_order' => $this->sort_order,
            'status' => $this->status,
        ];

        if (!$this->model->save()) {
            $this->addErrors($this->model->errors);
            return false;
        }

        return true;
    }
}
