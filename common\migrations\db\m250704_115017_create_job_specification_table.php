<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%job_category}}`.
 */
class m250704_115017_create_job_specification_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp(): void
    {
        $this->createTable('{{%job_specification}}', [
            'id' => $this->primaryKey(),
            'icon_id' => $this->integer(),
            'name_uz' => $this->string(),
            'name_ru' => $this->string(),
            'name_en' => $this->string(),
            'name_uzk' => $this->string(),
            'type' => $this->smallInteger(1),
            'created_at' => $this->integer(),
            'updated_at' => $this->integer(),
            'deleted_at' => $this->integer(),
            'created_by' => $this->integer(),
            'updated_by' => $this->integer(),
            'deleted_by' => $this->integer(),
        ]);

        $this->createIndex(
            'idx-job_specification-icon_id',
            '{{%job_specification}}',
            'icon_id'
        );
        $this->addForeignKey(
            'fk-job_specification-icon_id',
            '{{%job_specification}}',
            'icon_id',
            'file',
            'id',
            'SET NULL',
            'CASCADE'
        );

        $this->createIndex(
            'idx-job_specification-type',
            '{{%job_specification}}',
            'type'
        );

        $this->createIndex(
            'idx-job_specification-name_uz',
            'job_specification',
            'name_uz',
        );

        $this->createIndex(
            'idx-job_specification-name_ru',
            'job_specification',
            'name_ru'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown(): void
    {
        $this->dropTable('{{%job_specification}}');
    }
}
