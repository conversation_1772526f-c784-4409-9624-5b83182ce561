<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message/extract' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return [
    '"{attribute}" must be a valid JSON' => '"{attribute}" 必须是一个有效的JSON格式',
    'Active' => '活动',
    'Article ID' => '文章ID',
    'Article View' => '文章浏览',
    'Author' => '作者',
    'Base URL' => 'Base URL',
    'Base Url' => 'Base Url',
    'Body' => 'Body',
    'Caption' => '标题',
    'Carousel ID' => 'Carousel ID',
    'Category' => '分类',
    'Comment' => '评论',
    'Component' => '组件',
    'Config' => '配置',
    'Created At' => '创建时间',
    'Created at' => '创建时间',
    'Deleted' => '删除',
    'Down to maintenance.' => '维护中',
    'E-mail' => 'E-mail',
    'File Type' => '文件类型',
    'Firstname' => '名字',
    'Gender' => '性别',
    'ID' => 'ID',
    'Image' => '图片',
    'Key' => '键',
    'Last login' => '上次登录',
    'Lastname' => '姓',
    'Locale' => '本地',
    'Middlename' => '中间名',
    'Name' => '名字',
    'Order' => 'Order',
    'Page View' => '页面浏览',
    'Parent Category' => 'Parent Category',
    'Path' => 'Path',
    'Picture' => '照片',
    'Published' => '发布',
    'Published At' => '发布时间',
    'Size' => 'Size',
    'Slug' => 'Slug',
    'Status' => '状态',
    'Thumbnail' => '缩率图',
    'Title' => '标题',
    'Type' => 'Type',
    'Updated At' => '更新时间',
    'Updated at' => '更新时间',
    'Updater' => '上传者',
    'Upload Ip' => 'Upload Ip',
    'Url' => 'Url',
    'User ID' => '用户 ID',
    'Username' => '用户名',
    'Value' => '值',
    'API access token' => '',
    'Draft' => '',
    'Email' => '',
    'Expire At' => '',
    'Not Active' => '',
    'Password' => '',
    'Roles' => '',
    'Token' => '',
];
