a:14:{s:6:"config";s:8705:"a:5:{s:10:"phpVersion";s:6:"8.0.30";s:10:"yiiVersion";s:6:"2.0.46";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.46";s:4:"name";s:16:"Yii2 Starter Kit";s:7:"version";s:3:"1.0";s:8:"language";s:2:"uz";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";s:4:"true";}s:3:"php";a:5:{s:7:"version";s:6:"8.0.30";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:37:{s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:7:"2.0.7.0";s:5:"alias";a:1:{s:8:"@yii/jui";s:28:"/app/vendor/yiisoft/yii2-jui";}}s:22:"alexantr/yii2-elfinder";a:3:{s:4:"name";s:22:"alexantr/yii2-elfinder";s:7:"version";s:7:"1.8.0.0";s:5:"alias";a:1:{s:18:"@alexantr/elfinder";s:38:"/app/vendor/alexantr/yii2-elfinder/src";}}s:30:"asofter/yii2-imperavi-redactor";a:3:{s:4:"name";s:30:"asofter/yii2-imperavi-redactor";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:13:"@yii/imperavi";s:55:"/app/vendor/asofter/yii2-imperavi-redactor/yii/imperavi";}}s:30:"kartik-v/yii2-widget-typeahead";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-typeahead";s:7:"version";s:7:"1.0.4.0";s:5:"alias";a:1:{s:17:"@kartik/typeahead";s:46:"/app/vendor/kartik-v/yii2-widget-typeahead/src";}}s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:7:"3.0.5.0";s:5:"alias";a:1:{s:12:"@kartik/base";s:41:"/app/vendor/kartik-v/yii2-krajee-base/src";}}s:30:"kartik-v/yii2-widget-touchspin";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-touchspin";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:17:"@kartik/touchspin";s:46:"/app/vendor/kartik-v/yii2-widget-touchspin/src";}}s:31:"kartik-v/yii2-widget-timepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-timepicker";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:12:"@kartik/time";s:47:"/app/vendor/kartik-v/yii2-widget-timepicker/src";}}s:32:"kartik-v/yii2-widget-switchinput";a:3:{s:4:"name";s:32:"kartik-v/yii2-widget-switchinput";s:7:"version";s:7:"1.3.1.0";s:5:"alias";a:1:{s:19:"@kartik/switchinput";s:44:"/app/vendor/kartik-v/yii2-widget-switchinput";}}s:28:"kartik-v/yii2-widget-spinner";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-spinner";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/spinner";s:44:"/app/vendor/kartik-v/yii2-widget-spinner/src";}}s:28:"kartik-v/yii2-widget-sidenav";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-sidenav";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/sidenav";s:40:"/app/vendor/kartik-v/yii2-widget-sidenav";}}s:27:"kartik-v/yii2-widget-rating";a:3:{s:4:"name";s:27:"kartik-v/yii2-widget-rating";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:14:"@kartik/rating";s:43:"/app/vendor/kartik-v/yii2-widget-rating/src";}}s:31:"kartik-v/yii2-widget-rangeinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-rangeinput";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:13:"@kartik/range";s:47:"/app/vendor/kartik-v/yii2-widget-rangeinput/src";}}s:26:"kartik-v/yii2-widget-growl";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-growl";s:7:"version";s:7:"1.1.2.0";s:5:"alias";a:1:{s:13:"@kartik/growl";s:42:"/app/vendor/kartik-v/yii2-widget-growl/src";}}s:28:"kartik-v/yii2-widget-select2";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-select2";s:7:"version";s:7:"2.2.4.0";s:5:"alias";a:1:{s:15:"@kartik/select2";s:44:"/app/vendor/kartik-v/yii2-widget-select2/src";}}s:28:"kartik-v/yii2-widget-depdrop";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-depdrop";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:15:"@kartik/depdrop";s:44:"/app/vendor/kartik-v/yii2-widget-depdrop/src";}}s:30:"kartik-v/yii2-widget-fileinput";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-fileinput";s:7:"version";s:7:"1.1.1.0";s:5:"alias";a:1:{s:12:"@kartik/file";s:46:"/app/vendor/kartik-v/yii2-widget-fileinput/src";}}s:26:"kartik-v/yii2-widget-alert";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-alert";s:7:"version";s:7:"1.1.5.0";s:5:"alias";a:1:{s:13:"@kartik/alert";s:42:"/app/vendor/kartik-v/yii2-widget-alert/src";}}s:31:"kartik-v/yii2-widget-colorinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-colorinput";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:13:"@kartik/color";s:47:"/app/vendor/kartik-v/yii2-widget-colorinput/src";}}s:26:"kartik-v/yii2-widget-affix";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-affix";s:7:"version";s:7:"1.0.0.0";s:5:"alias";a:1:{s:13:"@kartik/affix";s:38:"/app/vendor/kartik-v/yii2-widget-affix";}}s:31:"kartik-v/yii2-widget-activeform";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-activeform";s:7:"version";s:7:"1.6.2.0";s:5:"alias";a:1:{s:12:"@kartik/form";s:47:"/app/vendor/kartik-v/yii2-widget-activeform/src";}}s:21:"kartik-v/yii2-widgets";a:3:{s:4:"name";s:21:"kartik-v/yii2-widgets";s:7:"version";s:7:"3.4.1.0";s:5:"alias";a:1:{s:15:"@kartik/widgets";s:37:"/app/vendor/kartik-v/yii2-widgets/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:7:"1.5.1.0";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:51:"/app/vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:31:"kartik-v/yii2-widget-datepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-datepicker";s:7:"version";s:7:"1.4.8.0";s:5:"alias";a:1:{s:12:"@kartik/date";s:47:"/app/vendor/kartik-v/yii2-widget-datepicker/src";}}s:20:"trntv/yii2-aceeditor";a:3:{s:4:"name";s:20:"trntv/yii2-aceeditor";s:7:"version";s:7:"2.1.2.0";s:5:"alias";a:1:{s:16:"@trntv/aceeditor";s:36:"/app/vendor/trntv/yii2-aceeditor/src";}}s:22:"trntv/yii2-command-bus";a:3:{s:4:"name";s:22:"trntv/yii2-command-bus";s:7:"version";s:7:"3.2.0.0";s:5:"alias";a:1:{s:10:"@trntv/bus";s:38:"/app/vendor/trntv/yii2-command-bus/src";}}s:18:"yiisoft/yii2-queue";a:3:{s:4:"name";s:18:"yiisoft/yii2-queue";s:7:"version";s:7:"2.3.4.0";s:5:"alias";a:11:{s:10:"@yii/queue";s:34:"/app/vendor/yiisoft/yii2-queue/src";s:13:"@yii/queue/db";s:45:"/app/vendor/yiisoft/yii2-queue/src/drivers/db";s:14:"@yii/queue/sqs";s:46:"/app/vendor/yiisoft/yii2-queue/src/drivers/sqs";s:15:"@yii/queue/amqp";s:47:"/app/vendor/yiisoft/yii2-queue/src/drivers/amqp";s:15:"@yii/queue/file";s:47:"/app/vendor/yiisoft/yii2-queue/src/drivers/file";s:15:"@yii/queue/sync";s:47:"/app/vendor/yiisoft/yii2-queue/src/drivers/sync";s:16:"@yii/queue/redis";s:48:"/app/vendor/yiisoft/yii2-queue/src/drivers/redis";s:16:"@yii/queue/stomp";s:48:"/app/vendor/yiisoft/yii2-queue/src/drivers/stomp";s:18:"@yii/queue/gearman";s:50:"/app/vendor/yiisoft/yii2-queue/src/drivers/gearman";s:20:"@yii/queue/beanstalk";s:52:"/app/vendor/yiisoft/yii2-queue/src/drivers/beanstalk";s:23:"@yii/queue/amqp_interop";s:55:"/app/vendor/yiisoft/yii2-queue/src/drivers/amqp_interop";}}s:26:"trntv/yii2-datetime-widget";a:3:{s:4:"name";s:26:"trntv/yii2-datetime-widget";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:19:"@trntv/yii/datetime";s:42:"/app/vendor/trntv/yii2-datetime-widget/src";}}s:16:"trntv/yii2-glide";a:3:{s:4:"name";s:16:"trntv/yii2-glide";s:7:"version";s:7:"1.2.2.0";s:5:"alias";a:1:{s:12:"@trntv/glide";s:32:"/app/vendor/trntv/yii2-glide/src";}}s:24:"rmrevin/yii2-fontawesome";a:3:{s:4:"name";s:24:"rmrevin/yii2-fontawesome";s:7:"version";s:7:"3.7.0.0";s:5:"alias";a:1:{s:24:"@rmrevin/yii/fontawesome";s:36:"/app/vendor/rmrevin/yii2-fontawesome";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"2.0.14.0";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:39:"/app/vendor/yiisoft/yii2-httpclient/src";}}s:30:"yii2-starter-kit/yii2-file-kit";a:3:{s:4:"name";s:30:"yii2-starter-kit/yii2-file-kit";s:7:"version";s:7:"2.1.5.0";s:5:"alias";a:1:{s:14:"@trntv/filekit";s:46:"/app/vendor/yii2-starter-kit/yii2-file-kit/src";}}s:23:"yiisoft/yii2-authclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-authclient";s:7:"version";s:8:"2.2.13.0";s:5:"alias";a:1:{s:15:"@yii/authclient";s:39:"/app/vendor/yiisoft/yii2-authclient/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.10.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:39:"/app/vendor/yiisoft/yii2-bootstrap4/src";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/faker";s:34:"/app/vendor/yiisoft/yii2-faker/src";}}s:24:"yiisoft/yii2-swiftmailer";a:3:{s:4:"name";s:24:"yiisoft/yii2-swiftmailer";s:7:"version";s:7:"2.1.3.0";s:5:"alias";a:1:{s:16:"@yii/swiftmailer";s:40:"/app/vendor/yiisoft/yii2-swiftmailer/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.5.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:32:"/app/vendor/yiisoft/yii2-gii/src";}}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.21.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:34:"/app/vendor/yiisoft/yii2-debug/src";}}}}";s:3:"log";s:29099:"a:1:{s:8:"messages";a:47:{i:0;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752496288.370059;i:4;a:0:{}i:5;i:1133920;}i:1;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1752496288.370077;i:4;a:0:{}i:5;i:1135096;}i:2;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752496288.381653;i:4;a:0:{}i:5;i:1140656;}i:3;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1752496288.434762;i:4;a:0:{}i:5;i:1229008;}i:4;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:1752496288.569882;i:4;a:0:{}i:5;i:1399656;}i:5;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752496288.605409;i:4;a:0:{}i:5;i:1417472;}i:12;a:6:{i:0;s:47:"Route requested: 'common/main/seen-by-category'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:1752496288.631162;i:4;a:0:{}i:5;i:1515624;}i:13;a:6:{i:0;s:22:"Loading module: common";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1752496288.63117;i:4;a:0:{}i:5;i:1517256;}i:14;a:6:{i:0;s:42:"Route to run: common/main/seen-by-category";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:1752496288.666412;i:4;a:0:{}i:5;i:1550128;}i:15;a:6:{i:0;s:65:"Opening DB connection: pgsql:host=db;port=5432;dbname=skillhub_db";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1752496289.121916;i:4;a:1:{i:0;a:5:{s:4:"file";s:27:"/app/common/models/User.php";s:4:"line";i:92;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:1935840;}i:18;a:6:{i:0;s:103:"SELECT * FROM "user" WHERE ("status"=2) AND ("access_token"='4_rLgtST8X-sgEvrIEYW0qq1DbQDG3qVQbfmwDOJ')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.132523;i:4;a:1:{i:0;a:5:{s:4:"file";s:27:"/app/common/models/User.php";s:4:"line";i:92;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:1938224;}i:21;a:6:{i:0;s:2810:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'user'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.161616;i:4;a:1:{i:0;a:5:{s:4:"file";s:27:"/app/common/models/User.php";s:4:"line";i:92;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:1969280;}i:24;a:6:{i:0;s:872:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='user'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.195489;i:4;a:1:{i:0;a:5:{s:4:"file";s:27:"/app/common/models/User.php";s:4:"line";i:92;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:1986232;}i:27;a:6:{i:0;s:51:"User '1' logged in from ********** with duration 0.";i:1;i:4;i:2;s:19:"yii\web\User::login";i:3;d:1752496289.440528;i:4;a:0:{}i:5;i:2088568;}i:28;a:6:{i:0;s:53:"UPDATE "user" SET "logged_at"=1752496289 WHERE "id"=1";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1752496289.440795;i:4;a:1:{i:0;a:5:{s:4:"file";s:48:"/app/common/behaviors/LoginTimestampBehavior.php";s:4:"line";i:35;s:8:"function";s:6:"__call";s:5:"class";s:18:"yii\base\Component";s:4:"type";s:2:"->";}}i:5;i:2100112;}i:31;a:6:{i:0;s:65:"Rate limit skipped: "user" does not implement RateLimitInterface.";i:1;i:4;i:2;s:37:"yii\filters\RateLimiter::beforeAction";i:3;d:1752496289.465884;i:4;a:0:{}i:5;i:2097824;}i:32;a:6:{i:0;s:85:"Running action: api\modules\common\controllers\MainController::actionSeenByCategory()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:1752496289.465925;i:4;a:0:{}i:5;i:2096672;}i:33;a:6:{i:0;s:256:"SELECT COUNT(*) FROM (SELECT "js"."id", "js"."name_uz" AS "name_uz", "js"."icon_id", COUNT(v.category_type_id) AS "vacancy_count" FROM "job_specification" "js" LEFT JOIN "vacancy" "v" ON v.category_type_id = js.id GROUP BY "js"."id" ORDER BY "js"."id") "c"";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.538745;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:120;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:2174536;}i:36;a:6:{i:0;s:238:"SELECT "js"."id", "js"."name_uz" AS "name_uz", "js"."icon_id", COUNT(v.category_type_id) AS "vacancy_count" FROM "job_specification" "js" LEFT JOIN "vacancy" "v" ON v.category_type_id = js.id GROUP BY "js"."id" ORDER BY "js"."id" LIMIT 10";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.549674;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:123;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:2180080;}i:39;a:6:{i:0;s:2823:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'job_specification'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.560958;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:123;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:2206288;}i:42;a:6:{i:0;s:885:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='job_specification'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.569531;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:123;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:2219960;}i:45;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.609486;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2311768;}i:48;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.610445;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2316376;}i:51;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.627651;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2347776;}i:54;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=2";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.629492;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2355464;}i:57;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=2";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.63011;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2360072;}i:60;a:6:{i:0;s:33:"SELECT * FROM "file" WHERE "id"=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.630683;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2365088;}i:63;a:6:{i:0;s:2810:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'file'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.631768;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2382624;}i:66;a:6:{i:0;s:872:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='file'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.637552;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2399648;}i:69;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=3";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.640824;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2405496;}i:72;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=3";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.642873;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2410104;}i:75;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.644373;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2413872;}i:78;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=4";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.645522;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2421184;}i:81;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=4";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.647081;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2425792;}i:84;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.648378;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2429560;}i:87;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=5";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.649545;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2436872;}i:90;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=5";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.652696;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2441480;}i:93;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.653467;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2445248;}i:96;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=6";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.654034;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2452560;}i:99;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=6";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.654595;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2457168;}i:102;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.65541;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2460936;}i:105;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.656812;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2468248;}i:108;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.658273;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2472856;}i:111;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.659784;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2476624;}i:114;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=8";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.661577;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2483936;}i:117;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=8";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.664419;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2488544;}i:120;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.665784;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2497944;}}}";s:9:"profiling";s:54462:"a:3:{s:6:"memory";i:2503080;s:4:"time";d:1.****************;s:8:"messages";a:70:{i:16;a:6:{i:0;s:65:"Opening DB connection: pgsql:host=db;port=5432;dbname=skillhub_db";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1752496289.121938;i:4;a:1:{i:0;a:5:{s:4:"file";s:27:"/app/common/models/User.php";s:4:"line";i:92;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:1936968;}i:17;a:6:{i:0;s:65:"Opening DB connection: pgsql:host=db;port=5432;dbname=skillhub_db";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1752496289.132483;i:4;a:1:{i:0;a:5:{s:4:"file";s:27:"/app/common/models/User.php";s:4:"line";i:92;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:1939600;}i:19;a:6:{i:0;s:103:"SELECT * FROM "user" WHERE ("status"=2) AND ("access_token"='4_rLgtST8X-sgEvrIEYW0qq1DbQDG3qVQbfmwDOJ')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.132553;i:4;a:1:{i:0;a:5:{s:4:"file";s:27:"/app/common/models/User.php";s:4:"line";i:92;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:1940640;}i:20;a:6:{i:0;s:103:"SELECT * FROM "user" WHERE ("status"=2) AND ("access_token"='4_rLgtST8X-sgEvrIEYW0qq1DbQDG3qVQbfmwDOJ')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.134183;i:4;a:1:{i:0;a:5:{s:4:"file";s:27:"/app/common/models/User.php";s:4:"line";i:92;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:1943728;}i:22;a:6:{i:0;s:2810:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'user'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.161703;i:4;a:1:{i:0;a:5:{s:4:"file";s:27:"/app/common/models/User.php";s:4:"line";i:92;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:1979984;}i:23;a:6:{i:0;s:2810:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'user'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.180504;i:4;a:1:{i:0;a:5:{s:4:"file";s:27:"/app/common/models/User.php";s:4:"line";i:92;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:2002096;}i:25;a:6:{i:0;s:872:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='user'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.195531;i:4;a:1:{i:0;a:5:{s:4:"file";s:27:"/app/common/models/User.php";s:4:"line";i:92;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:1990536;}i:26;a:6:{i:0;s:872:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='user'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.199633;i:4;a:1:{i:0;a:5:{s:4:"file";s:27:"/app/common/models/User.php";s:4:"line";i:92;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:1992104;}i:29;a:6:{i:0;s:53:"UPDATE "user" SET "logged_at"=1752496289 WHERE "id"=1";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1752496289.440852;i:4;a:1:{i:0;a:5:{s:4:"file";s:48:"/app/common/behaviors/LoginTimestampBehavior.php";s:4:"line";i:35;s:8:"function";s:6:"__call";s:5:"class";s:18:"yii\base\Component";s:4:"type";s:2:"->";}}i:5;i:2102376;}i:30;a:6:{i:0;s:53:"UPDATE "user" SET "logged_at"=1752496289 WHERE "id"=1";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1752496289.445603;i:4;a:1:{i:0;a:5:{s:4:"file";s:48:"/app/common/behaviors/LoginTimestampBehavior.php";s:4:"line";i:35;s:8:"function";s:6:"__call";s:5:"class";s:18:"yii\base\Component";s:4:"type";s:2:"->";}}i:5;i:2103640;}i:34;a:6:{i:0;s:256:"SELECT COUNT(*) FROM (SELECT "js"."id", "js"."name_uz" AS "name_uz", "js"."icon_id", COUNT(v.category_type_id) AS "vacancy_count" FROM "job_specification" "js" LEFT JOIN "vacancy" "v" ON v.category_type_id = js.id GROUP BY "js"."id" ORDER BY "js"."id") "c"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.538773;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:120;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:2177736;}i:35;a:6:{i:0;s:256:"SELECT COUNT(*) FROM (SELECT "js"."id", "js"."name_uz" AS "name_uz", "js"."icon_id", COUNT(v.category_type_id) AS "vacancy_count" FROM "job_specification" "js" LEFT JOIN "vacancy" "v" ON v.category_type_id = js.id GROUP BY "js"."id" ORDER BY "js"."id") "c"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.548341;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:120;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:2179680;}i:37;a:6:{i:0;s:238:"SELECT "js"."id", "js"."name_uz" AS "name_uz", "js"."icon_id", COUNT(v.category_type_id) AS "vacancy_count" FROM "job_specification" "js" LEFT JOIN "vacancy" "v" ON v.category_type_id = js.id GROUP BY "js"."id" ORDER BY "js"."id" LIMIT 10";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.549719;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:123;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:2182512;}i:38;a:6:{i:0;s:238:"SELECT "js"."id", "js"."name_uz" AS "name_uz", "js"."icon_id", COUNT(v.category_type_id) AS "vacancy_count" FROM "job_specification" "js" LEFT JOIN "vacancy" "v" ON v.category_type_id = js.id GROUP BY "js"."id" ORDER BY "js"."id" LIMIT 10";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.553116;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:123;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:2188352;}i:40;a:6:{i:0;s:2823:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'job_specification'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.561004;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:123;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:2217744;}i:41;a:6:{i:0;s:2823:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'job_specification'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.568822;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:123;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:2242224;}i:43;a:6:{i:0;s:885:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='job_specification'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.569578;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:123;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:2225016;}i:44;a:6:{i:0;s:885:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='job_specification'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.573716;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:123;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:2228216;}i:46;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.609521;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2314656;}i:47;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.610164;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2316264;}i:49;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.610463;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2319264;}i:50;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.610804;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2320872;}i:52;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.627697;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2349384;}i:53;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.629158;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2351424;}i:55;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=2";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.629514;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2358352;}i:56;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=2";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.629904;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2359960;}i:58;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=2";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.630139;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2362960;}i:59;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=2";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.630482;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2364568;}i:61;a:6:{i:0;s:33:"SELECT * FROM "file" WHERE "id"=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.630697;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2367520;}i:62;a:6:{i:0;s:33:"SELECT * FROM "file" WHERE "id"=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.631546;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2370368;}i:64;a:6:{i:0;s:2810:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'file'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.631808;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2393328;}i:65;a:6:{i:0;s:2810:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'file'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.637199;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2422448;}i:67;a:6:{i:0;s:872:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='file'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.637581;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2403952;}i:68;a:6:{i:0;s:872:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='file'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.640175;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2405520;}i:70;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=3";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.640901;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2408384;}i:71;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=3";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.642364;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2409992;}i:73;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=3";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.642917;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2412992;}i:74;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=3";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.643887;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2414600;}i:76;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.644438;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2415480;}i:77;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.645062;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2417520;}i:79;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.645566;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2424072;}i:80;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.64673;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2425680;}i:82;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.647118;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2428680;}i:83;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.647914;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2430288;}i:85;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.648415;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2431168;}i:86;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.649033;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2433208;}i:88;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=5";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.649614;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2439760;}i:89;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=5";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.652039;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2441368;}i:91;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=5";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.652726;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2444368;}i:92;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=5";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.653229;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2445976;}i:94;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.653483;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2446856;}i:95;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.653811;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2448896;}i:97;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=6";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.654051;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2455448;}i:98;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=6";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.654401;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2457056;}i:100;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=6";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.654609;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2460056;}i:101;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=6";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.654969;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2461664;}i:103;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.655474;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2462544;}i:104;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.656282;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2464584;}i:106;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.656852;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2471136;}i:107;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.657793;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2472744;}i:109;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.658319;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2475744;}i:110;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.659189;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2477352;}i:112;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.659823;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2478232;}i:113;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.660926;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2480272;}i:115;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=8";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.661623;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2486824;}i:116;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=8";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.663858;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2488432;}i:118;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=8";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.664508;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2491432;}i:119;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=8";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.665298;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2493040;}i:121;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.665822;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2499552;}i:122;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.666585;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2501592;}}}";s:2:"db";s:53737:"a:1:{s:8:"messages";a:68:{i:19;a:6:{i:0;s:103:"SELECT * FROM "user" WHERE ("status"=2) AND ("access_token"='4_rLgtST8X-sgEvrIEYW0qq1DbQDG3qVQbfmwDOJ')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.132553;i:4;a:1:{i:0;a:5:{s:4:"file";s:27:"/app/common/models/User.php";s:4:"line";i:92;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:1940640;}i:20;a:6:{i:0;s:103:"SELECT * FROM "user" WHERE ("status"=2) AND ("access_token"='4_rLgtST8X-sgEvrIEYW0qq1DbQDG3qVQbfmwDOJ')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.134183;i:4;a:1:{i:0;a:5:{s:4:"file";s:27:"/app/common/models/User.php";s:4:"line";i:92;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:1943728;}i:22;a:6:{i:0;s:2810:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'user'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.161703;i:4;a:1:{i:0;a:5:{s:4:"file";s:27:"/app/common/models/User.php";s:4:"line";i:92;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:1979984;}i:23;a:6:{i:0;s:2810:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'user'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.180504;i:4;a:1:{i:0;a:5:{s:4:"file";s:27:"/app/common/models/User.php";s:4:"line";i:92;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:2002096;}i:25;a:6:{i:0;s:872:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='user'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.195531;i:4;a:1:{i:0;a:5:{s:4:"file";s:27:"/app/common/models/User.php";s:4:"line";i:92;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:1990536;}i:26;a:6:{i:0;s:872:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='user'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.199633;i:4;a:1:{i:0;a:5:{s:4:"file";s:27:"/app/common/models/User.php";s:4:"line";i:92;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:1992104;}i:29;a:6:{i:0;s:53:"UPDATE "user" SET "logged_at"=1752496289 WHERE "id"=1";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1752496289.440852;i:4;a:1:{i:0;a:5:{s:4:"file";s:48:"/app/common/behaviors/LoginTimestampBehavior.php";s:4:"line";i:35;s:8:"function";s:6:"__call";s:5:"class";s:18:"yii\base\Component";s:4:"type";s:2:"->";}}i:5;i:2102376;}i:30;a:6:{i:0;s:53:"UPDATE "user" SET "logged_at"=1752496289 WHERE "id"=1";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1752496289.445603;i:4;a:1:{i:0;a:5:{s:4:"file";s:48:"/app/common/behaviors/LoginTimestampBehavior.php";s:4:"line";i:35;s:8:"function";s:6:"__call";s:5:"class";s:18:"yii\base\Component";s:4:"type";s:2:"->";}}i:5;i:2103640;}i:34;a:6:{i:0;s:256:"SELECT COUNT(*) FROM (SELECT "js"."id", "js"."name_uz" AS "name_uz", "js"."icon_id", COUNT(v.category_type_id) AS "vacancy_count" FROM "job_specification" "js" LEFT JOIN "vacancy" "v" ON v.category_type_id = js.id GROUP BY "js"."id" ORDER BY "js"."id") "c"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.538773;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:120;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:2177736;}i:35;a:6:{i:0;s:256:"SELECT COUNT(*) FROM (SELECT "js"."id", "js"."name_uz" AS "name_uz", "js"."icon_id", COUNT(v.category_type_id) AS "vacancy_count" FROM "job_specification" "js" LEFT JOIN "vacancy" "v" ON v.category_type_id = js.id GROUP BY "js"."id" ORDER BY "js"."id") "c"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.548341;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:120;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:2179680;}i:37;a:6:{i:0;s:238:"SELECT "js"."id", "js"."name_uz" AS "name_uz", "js"."icon_id", COUNT(v.category_type_id) AS "vacancy_count" FROM "job_specification" "js" LEFT JOIN "vacancy" "v" ON v.category_type_id = js.id GROUP BY "js"."id" ORDER BY "js"."id" LIMIT 10";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.549719;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:123;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:2182512;}i:38;a:6:{i:0;s:238:"SELECT "js"."id", "js"."name_uz" AS "name_uz", "js"."icon_id", COUNT(v.category_type_id) AS "vacancy_count" FROM "job_specification" "js" LEFT JOIN "vacancy" "v" ON v.category_type_id = js.id GROUP BY "js"."id" ORDER BY "js"."id" LIMIT 10";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.553116;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:123;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:2188352;}i:40;a:6:{i:0;s:2823:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'job_specification'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.561004;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:123;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:2217744;}i:41;a:6:{i:0;s:2823:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'job_specification'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.568822;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:123;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:2242224;}i:43;a:6:{i:0;s:885:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='job_specification'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.569578;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:123;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:2225016;}i:44;a:6:{i:0;s:885:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='job_specification'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.573716;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:123;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:2228216;}i:46;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.609521;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2314656;}i:47;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.610164;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2316264;}i:49;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.610463;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2319264;}i:50;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.610804;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2320872;}i:52;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.627697;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2349384;}i:53;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.629158;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2351424;}i:55;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=2";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.629514;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2358352;}i:56;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=2";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.629904;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2359960;}i:58;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=2";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.630139;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2362960;}i:59;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=2";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.630482;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2364568;}i:61;a:6:{i:0;s:33:"SELECT * FROM "file" WHERE "id"=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.630697;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2367520;}i:62;a:6:{i:0;s:33:"SELECT * FROM "file" WHERE "id"=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.631546;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2370368;}i:64;a:6:{i:0;s:2810:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'file'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.631808;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2393328;}i:65;a:6:{i:0;s:2810:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'file'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.637199;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2422448;}i:67;a:6:{i:0;s:872:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='file'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.637581;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2403952;}i:68;a:6:{i:0;s:872:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='file'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.640175;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2405520;}i:70;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=3";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.640901;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2408384;}i:71;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=3";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.642364;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2409992;}i:73;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=3";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.642917;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2412992;}i:74;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=3";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.643887;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2414600;}i:76;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.644438;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2415480;}i:77;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.645062;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2417520;}i:79;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.645566;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2424072;}i:80;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.64673;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2425680;}i:82;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.647118;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2428680;}i:83;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.647914;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2430288;}i:85;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.648415;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2431168;}i:86;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.649033;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2433208;}i:88;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=5";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.649614;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2439760;}i:89;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=5";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.652039;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2441368;}i:91;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=5";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.652726;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2444368;}i:92;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=5";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.653229;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2445976;}i:94;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.653483;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2446856;}i:95;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.653811;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2448896;}i:97;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=6";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.654051;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2455448;}i:98;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=6";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.654401;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2457056;}i:100;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=6";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.654609;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2460056;}i:101;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=6";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.654969;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2461664;}i:103;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.655474;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2462544;}i:104;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.656282;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2464584;}i:106;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.656852;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2471136;}i:107;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.657793;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2472744;}i:109;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.658319;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2475744;}i:110;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.659189;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2477352;}i:112;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.659823;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2478232;}i:113;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.660926;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2480272;}i:115;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=8";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.661623;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2486824;}i:116;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=8";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.663858;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2488432;}i:118;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=8";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.664508;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2491432;}i:119;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=8";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.665298;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2493040;}i:121;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.665822;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2499552;}i:122;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752496289.666585;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2501592;}}}";s:5:"event";s:12831:"a:71:{i:0;a:5:{s:4:"time";d:1752496288.631082;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:1;a:5:{s:4:"time";d:1752496288.669225;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:2;a:5:{s:4:"time";d:1752496288.669244;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"api\modules\common\Module";}i:3;a:5:{s:4:"time";d:1752496288.789272;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:29:"common\models\query\UserQuery";}i:4;a:5:{s:4:"time";d:1752496289.132473;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:5;a:5:{s:4:"time";d:1752496289.155671;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:6;a:5:{s:4:"time";d:1752496289.200106;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:7;a:5:{s:4:"time";d:1752496289.212871;s:4:"name";s:11:"beforeLogin";s:5:"class";s:17:"yii\web\UserEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\User";}i:8;a:5:{s:4:"time";d:1752496289.259825;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:9;a:5:{s:4:"time";d:1752496289.259879;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:10;a:5:{s:4:"time";d:1752496289.259888;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:11;a:5:{s:4:"time";d:1752496289.259894;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:12;a:5:{s:4:"time";d:1752496289.2599;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:13;a:5:{s:4:"time";d:1752496289.259906;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:14;a:5:{s:4:"time";d:1752496289.259911;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:15;a:5:{s:4:"time";d:1752496289.454787;s:4:"name";s:12:"beforeUpdate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:16;a:5:{s:4:"time";d:1752496289.465841;s:4:"name";s:11:"afterUpdate";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:17;a:5:{s:4:"time";d:1752496289.465852;s:4:"name";s:10:"afterLogin";s:5:"class";s:17:"yii\web\UserEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\User";}i:18;a:5:{s:4:"time";d:1752496289.465906;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:45:"api\modules\common\controllers\MainController";}i:19;a:5:{s:4:"time";d:1752496289.484414;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"api\modules\common\filters\MainFilter";}i:20;a:5:{s:4:"time";d:1752496289.48444;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"api\modules\common\filters\MainFilter";}i:21;a:5:{s:4:"time";d:1752496289.519352;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:22;a:5:{s:4:"time";d:1752496289.560891;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"api\modules\common\resources\MainResource";}i:23;a:5:{s:4:"time";d:1752496289.574038;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"api\modules\common\resources\MainResource";}i:24;a:5:{s:4:"time";d:1752496289.574069;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"api\modules\common\resources\MainResource";}i:25;a:5:{s:4:"time";d:1752496289.574421;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"api\modules\common\resources\MainResource";}i:26;a:5:{s:4:"time";d:1752496289.574509;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"api\modules\common\resources\MainResource";}i:27;a:5:{s:4:"time";d:1752496289.574535;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"api\modules\common\resources\MainResource";}i:28;a:5:{s:4:"time";d:1752496289.574554;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"api\modules\common\resources\MainResource";}i:29;a:5:{s:4:"time";d:1752496289.574588;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"api\modules\common\resources\MainResource";}i:30;a:5:{s:4:"time";d:1752496289.574633;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"api\modules\common\resources\MainResource";}i:31;a:5:{s:4:"time";d:1752496289.574638;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"api\modules\common\resources\MainResource";}i:32;a:5:{s:4:"time";d:1752496289.574646;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"api\modules\common\resources\MainResource";}i:33;a:5:{s:4:"time";d:1752496289.574665;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"api\modules\common\resources\MainResource";}i:34;a:5:{s:4:"time";d:1752496289.574669;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"api\modules\common\resources\MainResource";}i:35;a:5:{s:4:"time";d:1752496289.574673;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"api\modules\common\resources\MainResource";}i:36;a:5:{s:4:"time";d:1752496289.574676;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"api\modules\common\resources\MainResource";}i:37;a:5:{s:4:"time";d:1752496289.57468;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"api\modules\common\resources\MainResource";}i:38;a:5:{s:4:"time";d:1752496289.574737;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:45:"api\modules\common\controllers\MainController";}i:39;a:5:{s:4:"time";d:1752496289.593372;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:40;a:5:{s:4:"time";d:1752496289.610375;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:41;a:5:{s:4:"time";d:1752496289.627323;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:42;a:5:{s:4:"time";d:1752496289.629416;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:43;a:5:{s:4:"time";d:1752496289.630061;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:44;a:5:{s:4:"time";d:1752496289.630635;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:45;a:5:{s:4:"time";d:1752496289.631721;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"api\modules\common\resources\FileResource";}i:46;a:5:{s:4:"time";d:1752496289.640605;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"api\modules\common\resources\FileResource";}i:47;a:5:{s:4:"time";d:1752496289.640714;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:48;a:5:{s:4:"time";d:1752496289.642752;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:49;a:5:{s:4:"time";d:1752496289.644287;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:50;a:5:{s:4:"time";d:1752496289.64543;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:51;a:5:{s:4:"time";d:1752496289.646979;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:52;a:5:{s:4:"time";d:1752496289.648292;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:53;a:5:{s:4:"time";d:1752496289.64941;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:54;a:5:{s:4:"time";d:1752496289.652613;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:55;a:5:{s:4:"time";d:1752496289.653411;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:56;a:5:{s:4:"time";d:1752496289.653983;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:57;a:5:{s:4:"time";d:1752496289.654553;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:58;a:5:{s:4:"time";d:1752496289.655309;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:59;a:5:{s:4:"time";d:1752496289.656716;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:60;a:5:{s:4:"time";d:1752496289.658156;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:61;a:5:{s:4:"time";d:1752496289.659668;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:62;a:5:{s:4:"time";d:1752496289.661468;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:63;a:5:{s:4:"time";d:1752496289.664294;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:64;a:5:{s:4:"time";d:1752496289.665656;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:65;a:5:{s:4:"time";d:1752496289.666929;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"api\modules\common\Module";}i:66;a:5:{s:4:"time";d:1752496289.666944;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:67;a:5:{s:4:"time";d:1752496289.666958;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:68;a:5:{s:4:"time";d:1752496289.666979;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:69;a:5:{s:4:"time";d:1752496289.707213;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:70;a:5:{s:4:"time";d:1752496289.710112;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:91:"a:3:{s:5:"start";d:1752496288.224622;s:3:"end";d:1752496289.723155;s:6:"memory";i:2503080;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:1296:"a:3:{s:8:"messages";a:6:{i:6;a:6:{i:0;a:3:{s:4:"rule";s:5:"debug";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752496288.631132;i:4;a:0:{}i:5;i:1512160;}i:7;a:6:{i:0;a:3:{s:4:"rule";s:43:"debug/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752496288.631145;i:4;a:0:{}i:5;i:1512912;}i:8;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752496288.631149;i:4;a:0:{}i:5;i:1513664;}i:9;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752496288.631153;i:4;a:0:{}i:5;i:1514736;}i:10;a:6:{i:0;a:3:{s:4:"rule";s:41:"gii/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752496288.631155;i:4;a:0:{}i:5;i:1515488;}i:11;a:6:{i:0;s:55:"No matching URL rules. Using default URL parsing logic.";i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752496288.631156;i:4;a:0:{}i:5;i:1515864;}}s:5:"route";s:28:"common/main/seen-by-category";s:6:"action";s:69:"api\modules\common\controllers\MainController::actionSeenByCategory()";}";s:7:"request";s:5925:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:11:{s:6:"cookie";s:42:"PHPSESSID=f1392907cd41fdc24dd47eb21ec2c6b6";s:10:"connection";s:10:"keep-alive";s:15:"accept-encoding";s:17:"gzip, deflate, br";s:4:"host";s:17:"api.skill-hub.loc";s:13:"postman-token";s:36:"89439005-3ec5-4946-919c-3cd28526701a";s:13:"cache-control";s:8:"no-cache";s:6:"accept";s:3:"*/*";s:10:"user-agent";s:21:"PostmanRuntime/7.44.1";s:13:"authorization";s:47:"Bearer 4_rLgtST8X-sgEvrIEYW0qq1DbQDG3qVQbfmwDOJ";s:14:"content-length";s:0:"";s:12:"content-type";s:0:"";}s:15:"responseHeaders";a:11:{s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:10:"Set-Cookie";a:3:{i:0;s:60:"PHPSESSID=9d43e5c415ca6dbd3cf8be4999daf27d; path=/; HttpOnly";i:1;s:99:"_identity=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/; HttpOnly; SameSite=Lax";i:2;s:70:"_csrf=4SIa5ObI45t7FNjKco93ftAYI4ZvlA1u; path=/; HttpOnly; SameSite=Lax";}s:32:"Access-Control-Allow-Credentials";s:4:"true";s:4:"Vary";s:15:"Accept-Language";s:15:"X-Frame-Options";s:4:"DENY";s:12:"Content-Type";s:31:"application/json; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"6874f8a095565";s:16:"X-Debug-Duration";s:5:"1,484";s:12:"X-Debug-Link";s:37:"/debug/default/view?tag=6874f8a095565";}s:5:"route";s:28:"common/main/seen-by-category";s:6:"action";s:69:"api\modules\common\controllers\MainController::actionSeenByCategory()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:3:"GET";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:0:{}s:6:"SERVER";a:83:{s:17:"PHP_ENABLE_XDEBUG";s:1:"0";s:11:"PHP_USER_ID";s:2:"33";s:8:"HOSTNAME";s:12:"e2c384a79b49";s:11:"PHP_VERSION";s:6:"8.0.30";s:11:"PHP_INI_DIR";s:18:"/usr/local/etc/php";s:8:"GPG_KEYS";s:163:"1729F83938DA44E27BA0F4D3DBDB397470D12172 BFDDD28642824F8118EF77909B67A5C12229118F 2C16C765DBE54A088130F1BC4B9B5F600B55F3B4 39B641343D8C104B2B146DC3F9C39DC0B9698544";s:11:"PHP_LDFLAGS";s:12:"-Wl,-O1 -pie";s:3:"PWD";s:4:"/app";s:4:"HOME";s:8:"/var/www";s:10:"PHP_SHA256";s:64:"216ab305737a5d392107112d618a755dc5df42058226f1670e9db90e77d777d9";s:11:"PHPIZE_DEPS";s:76:"autoconf 		dpkg-dev 		file 		g++ 		gcc 		libc-dev 		make 		pkg-config 		re2c";s:4:"TERM";s:5:"linux";s:7:"PHP_URL";s:51:"https://www.php.net/distributions/php-8.0.30.tar.xz";s:5:"SHLVL";s:1:"0";s:24:"COMPOSER_ALLOW_SUPERUSER";s:1:"1";s:10:"PHP_CFLAGS";s:83:"-fstack-protector-strong -fpic -fpie -O2 -D_LARGEFILE_SOURCE -D_FILE_OFFSET_BITS=64";s:4:"PATH";s:108:"/app:/app/vendor/bin:/root/.composer/vendor/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin";s:11:"PHP_ASC_URL";s:55:"https://www.php.net/distributions/php-8.0.30.tar.xz.asc";s:12:"PHP_CPPFLAGS";s:83:"-fstack-protector-strong -fpic -fpie -O2 -D_LARGEFILE_SOURCE -D_FILE_OFFSET_BITS=64";s:4:"USER";s:8:"www-data";s:11:"HTTP_COOKIE";s:42:"PHPSESSID=f1392907cd41fdc24dd47eb21ec2c6b6";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:20:"HTTP_ACCEPT_ENCODING";s:17:"gzip, deflate, br";s:9:"HTTP_HOST";s:17:"api.skill-hub.loc";s:18:"HTTP_POSTMAN_TOKEN";s:36:"89439005-3ec5-4946-919c-3cd28526701a";s:18:"HTTP_CACHE_CONTROL";s:8:"no-cache";s:11:"HTTP_ACCEPT";s:3:"*/*";s:15:"HTTP_USER_AGENT";s:21:"PostmanRuntime/7.44.1";s:18:"HTTP_AUTHORIZATION";s:47:"Bearer 4_rLgtST8X-sgEvrIEYW0qq1DbQDG3qVQbfmwDOJ";s:15:"REDIRECT_STATUS";s:3:"200";s:11:"SERVER_NAME";s:17:"api.skill-hub.loc";s:11:"SERVER_PORT";s:2:"80";s:11:"SERVER_ADDR";s:10:"**********";s:11:"REMOTE_PORT";s:5:"59704";s:11:"REMOTE_ADDR";s:10:"**********";s:15:"SERVER_SOFTWARE";s:12:"nginx/1.28.0";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:14:"REQUEST_SCHEME";s:4:"http";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:13:"DOCUMENT_ROOT";s:12:"/app/api/web";s:12:"DOCUMENT_URI";s:10:"/index.php";s:11:"REQUEST_URI";s:29:"/common/main/seen-by-category";s:11:"SCRIPT_NAME";s:10:"/index.php";s:14:"CONTENT_LENGTH";s:0:"";s:12:"CONTENT_TYPE";s:0:"";s:14:"REQUEST_METHOD";s:3:"GET";s:12:"QUERY_STRING";s:0:"";s:15:"SCRIPT_FILENAME";s:22:"/app/api/web/index.php";s:9:"FCGI_ROLE";s:9:"RESPONDER";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:**********.548828;s:12:"REQUEST_TIME";i:**********;s:4:"argv";a:0:{}s:4:"argc";i:0;s:9:"YII_DEBUG";s:4:"true";s:7:"YII_ENV";s:3:"dev";s:15:"APP_MAINTENANCE";s:1:"0";s:11:"LINK_ASSETS";s:4:"true";s:6:"DB_DSN";s:42:"pgsql:host=db;port=5432;dbname=skillhub_db";s:11:"DB_USERNAME";s:8:"postgres";s:11:"DB_PASSWORD";s:8:"postgres";s:15:"DB_TABLE_PREFIX";s:0:"";s:11:"TEST_DB_DSN";s:38:"pgsql:host=db;port=5432;dbname=test_db";s:16:"TEST_DB_USERNAME";s:8:"postgres";s:16:"TEST_DB_PASSWORD";s:8:"postgres";s:20:"TEST_DB_TABLE_PREFIX";s:0:"";s:13:"API_HOST_INFO";s:37:"http://api.yii2-starter-kit.localhost";s:18:"FRONTEND_HOST_INFO";s:33:"http://yii2-starter-kit.localhost";s:17:"BACKEND_HOST_INFO";s:41:"http://backend.yii2-starter-kit.localhost";s:17:"STORAGE_HOST_INFO";s:41:"http://storage.yii2-starter-kit.localhost";s:9:"SMTP_HOST";s:11:"mailcatcher";s:9:"SMTP_PORT";s:4:"1025";s:30:"FRONTEND_COOKIE_VALIDATION_KEY";s:15:"<generated_key>";s:29:"BACKEND_COOKIE_VALIDATION_KEY";s:15:"<generated_key>";s:11:"ADMIN_EMAIL";s:32:"<EMAIL>";s:11:"ROBOT_EMAIL";s:32:"<EMAIL>";s:16:"GITHUB_CLIENT_ID";s:14:"your-client-id";s:20:"GITHUB_CLIENT_SECRET";s:18:"your-client-secret";s:14:"GLIDE_SIGN_KEY";s:15:"<generated_key>";s:20:"GLIDE_MAX_IMAGE_SIZE";s:7:"4000000";s:29:"COMPOSE_CONVERT_WINDOWS_PATHS";s:1:"1";s:18:"TELEGRAM_BOT_TOKEN";s:46:"**********************************************";s:25:"TELEGRAM_CONTACT_GROUP_ID";s:14:"-1002758503392";}s:3:"GET";a:0:{}s:4:"POST";a:0:{}s:6:"COOKIE";a:1:{s:9:"PHPSESSID";s:32:"f1392907cd41fdc24dd47eb21ec2c6b6";}s:5:"FILES";a:0:{}s:7:"SESSION";a:3:{s:7:"__flash";a:0:{}s:4:"__id";i:1;s:9:"__authKey";s:32:"K3gUyb6BOjNIiqElNxqU8solUTmWtuIe";}}";s:4:"user";s:2431:"a:5:{s:2:"id";i:1;s:8:"identity";a:12:{s:2:"id";s:1:"1";s:8:"username";s:15:"'administrator'";s:8:"auth_key";s:34:"'K3gUyb6BOjNIiqElNxqU8solUTmWtuIe'";s:12:"access_token";s:42:"'4_rLgtST8X-sgEvrIEYW0qq1DbQDG3qVQbfmwDOJ'";s:13:"password_hash";s:62:"'$2y$13$Ex1MqeSC55vR5SVg5lMh7.YZhkUgAXoXe5tM14xrq9U/NQZFf6XVq'";s:12:"oauth_client";s:4:"null";s:20:"oauth_client_user_id";s:4:"null";s:5:"email";s:27:"'<EMAIL>'";s:6:"status";s:1:"2";s:10:"created_at";s:10:"1752487996";s:10:"updated_at";s:10:"1752496280";s:9:"logged_at";s:10:"1752496289";}s:10:"attributes";a:12:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"Id";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:8:"Username";}i:2;a:2:{s:9:"attribute";s:8:"auth_key";s:5:"label";s:8:"Auth Key";}i:3;a:2:{s:9:"attribute";s:12:"access_token";s:5:"label";s:16:"API access token";}i:4;a:2:{s:9:"attribute";s:13:"password_hash";s:5:"label";s:13:"Password Hash";}i:5;a:2:{s:9:"attribute";s:12:"oauth_client";s:5:"label";s:12:"Oauth Client";}i:6;a:2:{s:9:"attribute";s:20:"oauth_client_user_id";s:5:"label";s:20:"Oauth Client User Id";}i:7;a:2:{s:9:"attribute";s:5:"email";s:5:"label";s:6:"E-mail";}i:8;a:2:{s:9:"attribute";s:6:"status";s:5:"label";s:6:"Status";}i:9;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:10:"Created at";}i:10;a:2:{s:9:"attribute";s:10:"updated_at";s:5:"label";s:10:"Updated at";}i:11;a:2:{s:9:"attribute";s:9:"logged_at";s:5:"label";s:10:"Last login";}}s:13:"rolesProvider";O:26:"yii\data\ArrayDataProvider":12:{s:3:"key";N;s:9:"allModels";a:0:{}s:10:"modelClass";N;s:2:"id";N;s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;}s:19:"permissionsProvider";O:26:"yii\data\ArrayDataProvider":12:{s:3:"key";N;s:9:"allModels";a:0:{}s:10:"modelClass";N;s:2:"id";s:4:"dp-1";s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;}}";s:5:"asset";s:6:"a:0:{}";s:7:"summary";a:12:{s:3:"tag";s:13:"6874f8a095565";s:3:"url";s:53:"http://api.skill-hub.loc/common/main/seen-by-category";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:10:"**********";s:4:"time";d:**********.548828;s:10:"statusCode";i:200;s:8:"sqlCount";i:34;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:2503080;s:14:"processingTime";d:1.****************;}s:10:"exceptions";a:0:{}}