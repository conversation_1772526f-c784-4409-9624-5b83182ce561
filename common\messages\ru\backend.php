<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message/extract' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return [
    'Account' => 'Аккаунт',
    'Application' => 'Приложение',
    'Application settings' => 'Настройки приложения',
    'Application timeline' => 'Хроника приложения',
    'Are you sure you want to delete this item?' => 'Вы уверены, что хотите удалить эту запись?',
    'Are you sure you want to flush this cache?' => 'Вы уверены, что хотите сбросить этот кеш?',
    'Article Categories' => 'Категории статей',
    'Articles' => 'Статьи',
    'CPU Usage' => 'Использование CPU',
    'Cache' => 'Кеш',
    'Cache entry has been successfully deleted' => 'Запись была успешно удалена из кеша',
    'Cache has been successfully flushed' => 'Кеш был успешно сброшен',
    'Carousel slide was successfully saved' => 'Слайд был успешно сохранён',
    'Category' => 'Категория',
    'Content' => 'Контент',
    'Create' => 'Создать',
    'Create {modelClass}' => 'Создание {modelClass}',
    'DB Type' => 'Тип БД',
    'DB Version' => 'Версия БД',
    'Date' => 'Дата',
    'Delete' => 'Удалить',
    'Delete a value with the specified key from cache' => 'Удалить значение по ключу из кеша',
    'Disabled' => 'Неактивно',
    'Edit account' => 'Редактировать аккаунт',
    'Edit profile' => 'Редактировать профиль',
    'Email' => 'Email',
    'Enabled' => 'Активно',
    'Error #{id}' => 'Ошибка #{id}',
    'Event' => 'Событие',
    'External IP' => 'Внешний IP',
    'Female' => 'Женский',
    'File Manager' => 'Менеджер файлов',
    'File Storage Items' => 'Записи о файлах',
    'Files in storage' => 'Файлов в хранилище',
    'Flush' => 'Сбросить',
    'Free Swap' => 'Свободно Swap',
    'Free memory' => 'Свободно памяти',
    'Hostname' => 'Имя хоста',
    'I18n Source Messages' => 'Тексты',
    'ID' => 'ID',
    'If you leave this field empty, the slug will be generated automatically' => 'Если вы оставите это поле пустым, ЧПУ будет сгенерирован автоматически',
    'Incorrect username or password.' => 'Неправильный логин или пароль',
    'Internal IP' => 'Внутренний IP',
    'Kernel version' => 'Версия ядра',
    'Key' => 'Ключ',
    'Key Storage Items' => 'Записи',
    'Key-Value Storage' => 'Ключ-Значение',
    'Language' => 'Язык',
    'Level' => 'Уровень',
    'Load average' => 'Средняя нагрузка',
    'Log Time' => 'Время события',
    'Logout' => 'Выход',
    'Logs' => 'Журнал',
    'Main' => 'Главная',
    'Male' => 'Мужской',
    'Member since {0, date, short}' => 'Участник с {0, date, short}',
    'Memory' => 'Память',
    'Memory Usage' => 'Использование памяти',
    'Message' => 'Сообщение',
    'More info' => 'Подробнее',
    'Network' => 'Сеть',
    'No events found' => 'Событий нет',
    'Number of cores' => 'Кол-во ядер',
    'OS' => 'ОС',
    'OS Release' => 'Версия ОС',
    'Off' => 'Выкл',
    'On' => 'Вкл',
    'Operating System' => 'Операционная система',
    'PHP Version' => 'Версия РHP',
    'Pages' => 'Страницы',
    'Password' => 'Пароль',
    'Password Confirm' => 'Подтверждение пароля',
    'Port' => 'Порт',
    'Prefix' => 'Префикс',
    'Processor' => 'Процессор',
    'Processor Architecture' => 'Архитектура процессора',
    'Profile' => 'Профиль',
    'Real time' => 'В режиме реального времени',
    'Remember Me' => 'Запомнить меня',
    'Reset' => 'Сбросить',
    'Save' => 'Сохранить',
    'Search' => 'Поиск',
    'Select cache' => 'Выберите кеш',
    'Settings was successfully saved' => 'Настройки были успешно сохранены',
    'Sign In' => 'Вход',
    'Software' => 'ПО',
    'Sorry, application failed to collect information about your system. See {link}.' => 'Извините, но приложению не удалось собрать информацию о вашей системе. Смотрите {link}.',
    'Static pages' => 'Статические страницы',
    'System' => 'Система',
    'System Date' => 'Системная дата',
    'System Information' => 'Информация о системе',
    'System Logs' => 'Системный журнал',
    'System Time' => 'Системное время',
    'Tag' => 'Тег',
    'TagDependency was invalidated' => 'TagDependency был инвалидирован',
    'Text Blocks' => 'Текстовые блоки',
    'This email has already been taken.' => 'Этот email уже кем-то используется.',
    'This username has already been taken.' => 'Это имя пользователя уже занято.',
    'Time' => 'Время',
    'Timeline' => 'Хроника',
    'Timezone' => 'Часовой пояс',
    'Total Swap' => 'Общий Swap',
    'Total memory' => 'Общая память',
    'Translation' => 'Перевод',
    'Update' => 'Редактировать',
    'Update {modelClass}: ' => 'Редактирование {modelClass}: ',
    'Uptime' => 'Uptime',
    'Used size' => 'Использованный размер',
    'User Registrations' => 'Регистраций',
    'Username' => 'Имя пользователя',
    'Users' => 'Пользователи',
    'View all' => 'Смотреть всё',
    'Web Server' => 'Веб-сервер',
    'Widget Carousel Items' => 'Слайды',
    'Widget Carousels' => 'Виджет карусели',
    'Widget Menus' => 'Виджеты меню',
    'You have new event' => 'У вас новое событие',
    'You have {num} log items' => 'У вас {num} записей в журнале',
    'Your account has been successfully saved' => 'Ваш аккаунт был успешно сохранён',
    'Your profile has been successfully saved' => 'Ваш профиль был успешно сохранён',
    'Add New {modelClass}' => '',
    'Assignments' => '',
    'Body small text' => '',
    'Brand small text' => '',
    'Cache Components' => '',
    'Cache elements' => '',
    'Carousel' => '',
    'Categories' => '',
    'Child' => '',
    'Child Items' => '',
    'Clear Logs' => '',
    'Collapsed sidebar' => '',
    'Create a new carousel' => '',
    'Create a new category' => '',
    'Create a new key storage item' => '',
    'Create a new menu' => '',
    'Create a new text block' => '',
    'Created At' => '',
    'Data' => '',
    'Description' => '',
    'Disable sidebar hover/focus auto expand' => '',
    'Files' => '',
    'Fixed footer' => '',
    'Fixed navbar' => '',
    'Fixed sidebar' => '',
    'Flush Cache' => '',
    'Footer small text' => '',
    'Go to logs' => '',
    'Go to timeline' => '',
    'Indent sidebar child menu items' => '',
    'Invalidate tag dependency' => '',
    'Item Name' => '',
    'Items' => '',
    'Login' => '',
    'Maintenance mode' => '',
    'Manager' => '',
    'Menu' => '',
    'Mini sidebar' => '',
    'Name' => '',
    'Navbar small text' => '',
    'New user! {identity} has signed up' => '',
    'No navbar border' => '',
    'Oops! Something went wrong... You may audit the error by reviewing the system logs or the application timeline.' => '',
    'Parent' => '',
    'Please select a child item...' => '',
    'Please select a parent item...' => '',
    'Please select a rule...' => '',
    'Please select a type...' => '',
    'Please select an item...' => '',
    'Please select an user...' => '',
    'RBAC Rules' => '',
    'Rule Name' => '',
    'Rules' => '',
    'Save Changes' => '',
    'Sidebar compact style' => '',
    'Sidebar flat style' => '',
    'Sidebar legacy style' => '',
    'Sidebar small text' => '',
    'Sign in to start your session' => '',
    'Storage' => '',
    'The logs have been cleared' => '',
    'Translations' => '',
    'Type' => '',
    'Updated At' => '',
    'Uploads' => '',
    'User' => '',
    'User ID' => '',
    'Widgets' => '',
    '{identity} has been deleted' => '',
];
