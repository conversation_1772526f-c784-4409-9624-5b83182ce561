<?php


namespace api\modules\common\filters;


use api\modules\common\resources\PartnerResource;
use common\base\BaseModel;

class PartnerFilter extends BaseModel
{
    public function getResult()
    {
        $query = PartnerResource::find()
            ->joinWith('file')
            ->where(['partner.deleted_at' => null]);

             return paginate($query->orderBy(['partner.id' => SORT_ASC]));

    }
}