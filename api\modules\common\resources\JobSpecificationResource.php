<?php

namespace api\modules\common\resources;

use common\models\JobSpecification;

class JobSpecificationResource extends JobSpecification
{
    public function fields(): array
    {
        return [
            'id',
            'icon',
            'name_uz',
            'name_ru',
            'name_en',
            'name_uzk',
        ];
    }

    public function getIcon()
    {
        return $this->hasOne(FileResource::class,['id' => 'icon_id']);
    }
}