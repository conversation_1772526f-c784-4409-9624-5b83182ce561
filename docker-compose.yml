version: "3.7"

services:
  app:
    build: frontend
    restart: always
    volumes:
      # Mount source-code for development
      - ./:/app
  nginx:
    image: nginx:stable-alpine
    restart: always
    ports:
      - 80:80
    volumes:
      - ./:/app
      - ./docker/nginx/vhost.conf:/etc/nginx/conf.d/vhost.conf
    depends_on:
      - app
  db:
    image: postgres:14
    restart: always
    shm_size: 128mb
    volumes:
      - postgres-data:/var/lib/postgresql/data
    ports:
      - 5432:5432
    environment:
      POSTGRES_PASSWORD: postgres

  adminer:
    image: adminer
    restart: always
    ports:
      - 8080:8080
networks:
  php-project-network:
volumes:
  postgres-data: