<?php

namespace api\modules\common\controllers;

use api\modules\common\filters\PageFilter;
use api\modules\common\forms\PageDeleteForm;
use api\modules\common\forms\PageForm;
use api\modules\common\forms\PageUpdateForm;
use api\modules\common\resources\PageResource;
use common\base\BaseController;
use Yii;
use yii\web\NotFoundHttpException;

class PageController extends BaseController
{
    public function actionIndex(): array
    {
        return $this->sendResponse(
            new PageFilter(),
            Yii::$app->request->queryParams
        );
    }

    /**
     * @throws NotFoundHttpException
     */
    public function actionView($id): array
    {
        return $this->sendModel($this->findOne($id));
    }

    public function actionCreate(): array
    {
        return $this->sendResponse(
            new PageForm(new PageResource()),
            Yii::$app->request->bodyParams
        );
    }

    /**
     * @throws NotFoundHttpException
     */
    public function actionUpdate($id): array
    {
        return $this->sendResponse(
            new PageUpdateForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }

    /**
     * @throws NotFoundHttpException
     */
    public function actionDelete($id): array
    {
        return $this->sendResponse(
            new PageDeleteForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }

    /**
     * @throws NotFoundHttpException
     */
    protected function findOne($id): PageResource
    {
        $model =  PageResource::findOne(['id' => $id,'deleted_at' => null]);
        if (!$model) {
            throw new NotFoundHttpException('The page page does not exist.');
        }
        return $model;
    }
}