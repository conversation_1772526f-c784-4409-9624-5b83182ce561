<?php

namespace api\modules\common\resources;

use common\enums\CurrencyEnum;
use common\models\Vacancy;
use Yii;

class VacancyResource extends Vacancy
{
    public function fields(): array
    {
        return [
            'id',
            'company',
            'title_uz',
            'title_ru',
            'description_uz',
            'description_ru',
            'jobCategory',
            'jobWork',
            'salary_from',
            'salary_to',
            'currency' => function (VacancyResource $model) {
                return array_key_exists($model->currency_code,CurrencyEnum::CURRENCY_CODE_SIGN_LIST) ? CurrencyEnum::CURRENCY_CODE_SIGN_LIST[$model->currency_code] : null;
            },
            'currency_code',
            'status',
            'published' => function (VacancyResource $model) {
                return Yii::$app->formatter->asRelativeTime($model->published_at);
            },
            'location',
            'latitude',
            'longitude',
        ];
    }

    /**
     * Gets query for [[JobCategory]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getJobCategory()
    {
        return $this->hasOne(JobSpecificationResource::class, ['id' => 'category_type_id']);
    }

    /**
     * Gets query for [[JobWork]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getJobWork()
    {
        return $this->hasOne(JobSpecificationResource::class, ['id' => 'work_type_id']);
    }

    public function getCompany()
    {
        return $this->hasOne(CompanyResource::class,['id' => 'company_id']);
    }
}