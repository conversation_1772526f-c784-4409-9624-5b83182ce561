<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message/extract' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return [
    'Account' => 'Tài khoản',
    'Application' => 'Ứng dụng',
    'Application settings' => 'Cấu hình ứng dụng',
    'Application timeline' => 'Dòng thời gian',
    'Are you sure you want to delete this item?' => 'Bạn có muốn xóa mục này?',
    'Are you sure you want to flush this cache?' => 'Bạn có muốn dọn bộ nhớ cache?',
    'Article Categories' => 'Danh mục bài viết',
    'Articles' => 'Bài viết',
    'CPU Usage' => 'CPU đã sử dụng',
    'Cache' => 'Cache',
    'Cache entry has been successfully deleted' => 'Bộ nhớ cache đã được xóa thành công',
    'Cache has been successfully flushed' => 'Bộ nhớ cache đã được dọn thành công',
    'Carousel slide was successfully saved' => 'Carousel slide được lưu thành công',
    'Category' => 'Danh mục',
    'Content' => 'Nội dung',
    'Create' => 'Tạo mới',
    'Create {modelClass}' => 'Tạo mới {modelClass}',
    'DB Type' => 'Loại DB',
    'DB Version' => 'Phiên bản DB',
    'Date' => 'Ngày',
    'Delete' => 'Xóa',
    'Delete a value with the specified key from cache' => 'Xóa giá trị cùng với từ khóa từ bộ nhớ cache',
    'Disabled' => 'Gỡ bỏ',
    'Edit account' => 'Cập nhật tài khoản',
    'Edit profile' => 'Cập nhật hồ sơ',
    'Email' => 'Email',
    'Enabled' => 'Bật lên',
    'Error #{id}' => 'Lỗi #{id}',
    'Event' => 'Sự kiện',
    'External IP' => 'IP Ngoài',
    'Female' => 'Nữ',
    'File Manager' => 'Quản lý file',
    'File Storage Items' => 'Các mục file',
    'Files in storage' => 'File trong bộ nhớ',
    'Flush' => 'Dọn',
    'Free Swap' => 'Bộ nhớ Swap còn trống',
    'Free memory' => 'Bộ nhớ còn trống',
    'Hostname' => 'Tên host',
    'I18n Source Messages' => 'I18n Source Messages',
    'ID' => 'ID',
    'If you leave this field empty, the slug will be generated automatically' => 'Nếu bạn để trống, slug sẽ được tạo tự động',
    'Incorrect username or password.' => 'Sai tên đăng nhập hoặc mật khẩu',
    'Internal IP' => 'Internal IP',
    'Kernel version' => 'Phiên bản nhân',
    'Key' => 'Khóa',
    'Key Storage Items' => 'Key Storage Items',
    'Key-Value Storage' => 'Key-Value Storage',
    'Language' => 'Ngôn ngữ',
    'Level' => 'Mức',
    'Load average' => 'Load average',
    'Log Time' => 'Log Time',
    'Logout' => 'Đăng xuất',
    'Logs' => 'Logs',
    'Main' => 'Chính',
    'Male' => 'Nam',
    'Member since {0, date, short}' => 'Thành viên từ {0, date, short}',
    'Memory' => 'Bộ nhớ',
    'Memory Usage' => 'Bộ nhớ đã dùng',
    'Message' => 'Thông điệp',
    'More info' => 'Thêm thông tin',
    'Network' => 'Mạng',
    'No events found' => 'Không có sự kiện',
    'Number of cores' => 'Số nhân',
    'OS' => 'ОС',
    'OS Release' => 'Phiên bản ОС',
    'Off' => 'Tắt',
    'On' => 'Bật',
    'Operating System' => 'Hệ điều hành',
    'PHP Version' => 'Phiên bản PHP',
    'Pages' => 'Danh sách trang',
    'Password' => 'Mật khẩu',
    'Password Confirm' => 'Xác nhận mật khẩu',
    'Port' => 'Cổng',
    'Prefix' => 'Tiền tố',
    'Processor' => 'Bộ vi xử lý',
    'Processor Architecture' => 'Kiến trúc hệ thống',
    'Profile' => 'Hồ sơ',
    'Real time' => 'Thời gian thực',
    'Remember Me' => 'Nhớ thông tin',
    'Reset' => 'Chỉnh lại',
    'Save' => 'Lưu vào',
    'Search' => 'Tìm kiếm',
    'Select cache' => 'Chọn bộ nhớ cache',
    'Settings was successfully saved' => 'Cấu hình đã lưu thành công',
    'Sign In' => 'Đăng nhập',
    'Software' => 'Phần mềm',
    'Sorry, application failed to collect information about your system. See {link}.' => 'Có lỗi xảy ra, ứng dụng không thể nhận các thông tin về hệ thống. Xem {link}',
    'Static pages' => 'Trang tĩnh',
    'System' => 'Hệ thống',
    'System Date' => 'Giờ hệ thống',
    'System Information' => 'Thông tin hệ thống',
    'System Logs' => 'System Logs',
    'System Time' => 'Giờ hệ thống',
    'Tag' => 'Thẻ',
    'TagDependency was invalidated' => 'TagDependency đã được vô hiệu hóa',
    'Text Blocks' => 'Text Blocks',
    'This email has already been taken.' => 'Email này đã tồn tại',
    'This username has already been taken.' => 'Tên đăng nhập đã tồn tại',
    'Time' => 'Giờ',
    'Timeline' => 'Dòng thời gian',
    'Timezone' => 'Múi giờ',
    'Total Swap' => 'Total Swap',
    'Total memory' => 'Tổng bộ nhớ',
    'Translation' => 'Dịch',
    'Update' => 'Cập nhật',
    'Update {modelClass}: ' => 'Cập nhật {modelClass}:',
    'Uptime' => 'Uptime',
    'Used size' => 'Dung lượng sử dụng',
    'User Registrations' => 'Đăng ký thành viên',
    'Username' => 'Tên đăng nhập',
    'Users' => 'Thành viên',
    'View all' => 'Xem cả',
    'Web Server' => 'Máy chủ Web',
    'Widget Carousel Items' => 'Widget Carousel Items',
    'Widget Carousels' => 'idget Carousels',
    'Widget Menus' => 'Widget Menus',
    'You have new event' => 'Sự kiện mới',
    'You have {num} log items' => '{num} записів у журналі',
    'Your account has been successfully saved' => 'Lưu thông tin tài khoản thành công',
    'Your profile has been successfully saved' => 'Lưu thông tin hồ sơ thành công',
    'Add New {modelClass}' => '',
    'Assignments' => '',
    'Body small text' => '',
    'Brand small text' => '',
    'Cache Components' => '',
    'Cache elements' => '',
    'Carousel' => '',
    'Categories' => '',
    'Child' => '',
    'Child Items' => '',
    'Clear Logs' => '',
    'Collapsed sidebar' => '',
    'Create a new carousel' => '',
    'Create a new category' => '',
    'Create a new key storage item' => '',
    'Create a new menu' => '',
    'Create a new text block' => '',
    'Created At' => '',
    'Data' => '',
    'Description' => '',
    'Disable sidebar hover/focus auto expand' => '',
    'Files' => '',
    'Fixed footer' => '',
    'Fixed navbar' => '',
    'Fixed sidebar' => '',
    'Flush Cache' => '',
    'Footer small text' => '',
    'Go to logs' => '',
    'Go to timeline' => '',
    'Indent sidebar child menu items' => '',
    'Invalidate tag dependency' => '',
    'Item Name' => '',
    'Items' => '',
    'Login' => '',
    'Maintenance mode' => '',
    'Manager' => '',
    'Menu' => '',
    'Mini sidebar' => '',
    'Name' => '',
    'Navbar small text' => '',
    'New user! {identity} has signed up' => '',
    'No navbar border' => '',
    'Oops! Something went wrong... You may audit the error by reviewing the system logs or the application timeline.' => '',
    'Parent' => '',
    'Please select a child item...' => '',
    'Please select a parent item...' => '',
    'Please select a rule...' => '',
    'Please select a type...' => '',
    'Please select an item...' => '',
    'Please select an user...' => '',
    'RBAC Rules' => '',
    'Rule Name' => '',
    'Rules' => '',
    'Save Changes' => '',
    'Sidebar compact style' => '',
    'Sidebar flat style' => '',
    'Sidebar legacy style' => '',
    'Sidebar small text' => '',
    'Sign in to start your session' => '',
    'Storage' => '',
    'The logs have been cleared' => '',
    'Translations' => '',
    'Type' => '',
    'Updated At' => '',
    'Uploads' => '',
    'User' => '',
    'User ID' => '',
    'Widgets' => '',
    '{identity} has been deleted' => '',
];
