<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%region}}`.
 */
class m250704_130152_create_region_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp(): void
    {
        $this->createTable('{{%region}}', [
            'id' => $this->primaryKey(),

            'parent_id' => $this->integer(),
            'type' => $this->integer(),

            'title_ru' => $this->string(),
            'title_en' => $this->string(),
            'title_uz' => $this->string(),
            'title_uzk' => $this->string(),

            'created_at' => $this->integer(),
            'updated_at' => $this->integer(),
            'deleted_at' => $this->integer(),
            'created_by' => $this->integer(),
            'updated_by' => $this->integer(),
            'deleted_by' => $this->integer(),
        ]);

        $this->createIndex(
            'idx-region-type',
            '{{%region}}',
            'type'
        );

        $this->createIndex("idx-region-parent_id", "region", 'parent_id');

        $this->addForeignKey("fk-region-parent_id-region-id", 'region', 'parent_id', 'region', 'id', 'cascade', 'cascade');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown(): void
    {
        $this->dropTable('{{%region}}');
    }
}
