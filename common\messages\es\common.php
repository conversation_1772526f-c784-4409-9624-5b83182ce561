<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message/extract' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return [
    '"{attribute}" must be a valid JSON' => '"{attribute}" debe de ser JSON válido',
    'API access token' => 'Token de acceso de la API',
    'Active' => 'Activo',
    'Article ID' => 'ID Artículo',
    'Article View' => 'Vista de Artículo',
    'Author' => 'Autor',
    'Base URL' => 'URL Base',
    'Base Url' => 'Url Base',
    'Body' => 'Contenido',
    'Caption' => 'Subtítulo',
    'Carousel ID' => 'ID de Carrusel',
    'Category' => 'Categoría',
    'Comment' => 'Comentario',
    'Component' => 'Componente',
    'Config' => 'Configuración',
    'Created At' => 'Añadido el',
    'Created at' => 'Añadido el',
    'Deleted' => 'Eliminado',
    'Down to maintenance.' => 'Desactivado por mantenimiento.',
    'E-mail' => 'E-mail',
    'Email' => 'Correo',
    'Expire At' => 'Expira el',
    'File Type' => 'Tipo de Archivo',
    'Firstname' => 'Primer Nombre',
    'Gender' => 'Sexo',
    'ID' => 'ID',
    'Image' => 'Imagen',
    'Key' => 'Llave',
    'Last login' => 'Último Ingreso',
    'Lastname' => 'Apellido',
    'Locale' => 'Lenguaje',
    'Middlename' => 'Segundo Nombre',
    'Name' => 'Nombre',
    'Not Active' => 'Inactivo',
    'Order' => 'Orden',
    'Page View' => 'Vista de Página',
    'Parent Category' => 'Categoría Superior',
    'Password' => 'Contraseña',
    'Path' => 'Ruta',
    'Picture' => 'Imagen',
    'Published' => 'Publicado',
    'Published At' => 'Publicado el',
    'Roles' => 'Roles',
    'Size' => 'Tamaño',
    'Slug' => 'Slug',
    'Status' => 'Estado',
    'Thumbnail' => 'Miniatura',
    'Title' => 'Título',
    'Token' => 'Token',
    'Type' => 'Tipo',
    'Updated At' => 'Modificado el',
    'Updated at' => 'Modificado el',
    'Updater' => 'Modificado por',
    'Upload Ip' => 'IP',
    'Url' => 'Url',
    'User ID' => 'ID de Usuario',
    'Username' => 'Nombre de Usuario',
    'Value' => 'Valor',
    'Draft' => 'Borrador',
];
