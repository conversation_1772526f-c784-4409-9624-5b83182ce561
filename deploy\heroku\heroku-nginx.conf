charset utf-8;
client_max_body_size 32m;

location /backend {
    try_files $uri /backend/web/index.php?$args;
}

location /storage {
    try_files $uri /storage/web/index.php?$args;
}

location /frontend {
    try_files $uri /frontend/web/index.php?$args;
}

location / {
    try_files /frontend/web/$uri /frontend/web/index.php?$args;
}

location ~ \.php$ {
    fastcgi_split_path_info ^(.+\.php)(/.+)$;
    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    fastcgi_pass heroku-fcgi;
    fastcgi_index index.php;
    include fastcgi_params;
}
