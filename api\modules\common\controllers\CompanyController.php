<?php

namespace api\modules\common\controllers;

use Yii;
use api\modules\common\filters\CompanyFilter;
use api\modules\common\forms\CompanyDeleteForm;
use api\modules\common\forms\CompanyForm;
use api\modules\common\forms\CompanyUpdateForm;
use api\modules\common\resources\CompanyResource;
use common\base\BaseController;
use yii\web\NotFoundHttpException;

class CompanyController extends BaseController
{
    public function actionIndex(): array
    {
        return $this->sendResponse(
            new CompanyFilter(),
            Yii::$app->request->queryParams
        );
    }

    /**
     * @throws NotFoundHttpException
     */
    public function actionView($id): array
    {
        return $this->sendModel($this->findOne($id));
    }

    public function actionCreate(): array
    {
        return $this->sendResponse(
            new CompanyForm(new CompanyResource()),
            Yii::$app->request->bodyParams,
        );
    }

    /**
     * @throws NotFoundHttpException
     */
    public function actionUpdate($id): array
    {
        return $this->sendResponse(
            new CompanyUpdateForm($this->findOne($id)),
            Yii::$app->request->bodyParams,
        );
    }

    /**
     * @throws NotFoundHttpException
     */
    public function actionDelete($id): array
    {
        return $this->sendResponse(
            new CompanyDeleteForm($this->findOne($id)),
            Yii::$app->request->bodyParams,
        );
    }

    /**
     * @throws NotFoundHttpException
     */
    protected function findOne($id): CompanyResource
    {
        $model = CompanyResource::findOne(['id' => $id,'deleted_at' => null]);
        if (!$model) {
            throw new NotFoundHttpException('The model page does not exist.');
        }
        return $model;
    }
}