<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message/extract' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return [
    'API access token' => 'Token akses API',
    'Active' => 'Aktif',
    'Article ID' => 'ID Artikel',
    'Article View' => 'Lihat Artikel',
    'Author' => 'Penulis',
    'Base URL' => 'Basis URL',
    'Base Url' => 'Basis Url',
    'Body' => 'Badan',
    'Caption' => 'Caption',
    'Carousel ID' => 'ID Karusel',
    'Category' => 'Kategori',
    'Comment' => 'Komentar',
    'Component' => 'Komponen',
    'Config' => 'Konfigurasi',
    'Created At' => 'Dibuat pada',
    'Created at' => 'Dibuat pada',
    'Deleted' => 'Dihapus',
    'Down to maintenance.' => 'Sedang perbaikan.',
    'E-mail' => 'Surel',
    'Email' => 'Surel',
    'Expire At' => 'Kadaluarsa pada',
    'File Type' => 'Tipe berkas',
    'Firstname' => 'Nama depan',
    'Gender' => 'Jenis Kelamin',
    'ID' => 'ID',
    'Image' => 'Gambar',
    'Key' => 'Kunci',
    'Last login' => 'Terakhir masuk',
    'Lastname' => 'Nama Belakang',
    'Locale' => 'Lokal',
    'Middlename' => 'Nama Tengah',
    'Name' => 'Nama',
    'Not Active' => 'Tidak aktif',
    'Order' => 'Urutkan',
    'Page View' => 'Lihat halaman',
    'Parent Category' => 'Kategori Induk',
    'Password' => 'Password',
    'Path' => 'Path',
    'Picture' => 'Gambar',
    'Published' => 'Terpublikasi',
    'Published At' => 'Dipublikasikan pada',
    'Roles' => 'Roles',
    'Size' => 'Ukuran',
    'Slug' => 'Slug',
    'Status' => 'Status',
    'Thumbnail' => 'Thumbnail',
    'Title' => 'Judul',
    'Token' => 'Token',
    'Type' => 'Tipe',
    'Updated At' => 'Diperbaharui Pada',
    'Updated at' => 'Diperbaharui pada',
    'Updater' => 'Pembaharu',
    'Upload Ip' => 'Upload Ip',
    'Url' => 'URL',
    'User ID' => 'User ID',
    'Username' => 'Username',
    'Value' => 'Nilai',
    '"{attribute}" must be a valid JSON' => '',
    'Draft' => 'Draf',
];
