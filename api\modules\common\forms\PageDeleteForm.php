<?php

namespace api\modules\common\forms;

use api\modules\common\resources\PageResource;
use common\base\BaseModel;
use common\enums\PageEnum;
use common\enums\StatusEnum;
use yii\db\StaleObjectException;

class PageDeleteForm extends BaseModel
{
    public function __construct(
        public ?PageResource $model = null,
        $config = [],
    )
    {
        parent::__construct($config);
    }

    /**
     * @throws \Throwable
     * @throws StaleObjectException
     */
    public function getResult(): bool|int
    {
        $this->model->status = StatusEnum::STATUS_DELETED;
        return $this->model->delete();
    }
}