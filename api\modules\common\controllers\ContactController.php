<?php

namespace api\modules\common\controllers;

use Yii;
use api\modules\common\forms\ContactSendMessageForm;
use common\base\BaseController;
use common\components\services\BotService;

class ContactController extends BaseController
{
    public function actionSendMessageToGroup(): array
    {
        return $this->sendResponse(
            new ContactSendMessageForm(new BotService()),
            Yii::$app->request->bodyParams,
        );
    }
}