<?php

namespace api\modules\common\forms;

use api\modules\common\resources\PartnerResource;
use common\base\BaseModel;

class PartnerDeleteForm extends BaseModel
{
    public function __construct(
        public ?PartnerResource $model = null,
        $config = []
    ) {
        parent::__construct($config);
    }

    public function rules(): array
    {
        return [];
    }

    public function getResult(): bool
    {
        if (!$this->model) {
            $this->addError('model', 'Model topilmadi.');
            return false;
        }

        // Soft delete
        if (!$this->model->delete()) {
            $this->addErrors($this->model->errors);
            return false;
        }

        return true;
    }
}
