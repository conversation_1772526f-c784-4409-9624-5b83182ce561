<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message/extract' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return [
    'Account' => 'Compte',
    'Application' => 'Application',
    'Application settings' => 'Paramètres de l\'application',
    'Application timeline' => 'Calendrier d\'application',
    'Are you sure you want to delete this item?' => 'Êtes-vous sûr de bien vouloir supprimer cet élément?',
    'Are you sure you want to flush this cache?' => 'Êtes-vous sûr de vouloir vider ce cache?',
    'Article Categories' => 'Catégories d\'articles',
    'Articles' => 'Des articles',
    'CPU Usage' => 'L\'utilisation du processeur',
    'Cache' => 'Cache',
    'Cache entry has been successfully deleted' => 'L\'entrée de la cache a été supprimée avec succès',
    'Cache has been successfully flushed' => 'Le cache a été vidé avec succès',
    'Carousel slide was successfully saved' => 'La diapositive du carrousel a été enregistrée avec succès',
    'Category' => 'Catégorie',
    'Content' => 'Contenu',
    'Create' => 'Créer',
    'Create {modelClass}' => 'Créer {modelClass}',
    'DB Type' => 'Type de base de données',
    'DB Version' => 'Version DB',
    'Date' => 'Rendez-vous amoureux',
    'Delete' => 'Effacer',
    'Delete a value with the specified key from cache' => 'Supprimer une valeur avec la clé spécifiée du cache',
    'Disabled' => 'désactivé',
    'Edit account' => 'Modifier le compte',
    'Edit profile' => 'Editer le profil',
    'Email' => 'Email',
    'Enabled' => 'Activée',
    'Error #{id}' => 'Erreur # {id}',
    'Event' => 'un événement',
    'External IP' => 'IP externe',
    'Female' => 'Femelle',
    'File Manager' => 'Gestionnaire de fichiers',
    'File Storage Items' => 'Articles de stockage de fichiers',
    'Files in storage' => 'Fichiers en stockage',
    'Flush' => 'Affleurer',
    'Free Swap' => 'Échange gratuit',
    'Free memory' => 'Mémoire libre',
    'Hostname' => 'Nom d\'hôte',
    'I18n Source Messages' => 'I18n Source Messages',
    'ID' => 'ID',
    'If you leave this field empty, the slug will be generated automatically' => 'Si vous laissez ce champ vide, le slug sera généré automatiquement',
    'Incorrect username or password.' => 'Identifiant ou mot de passe incorrect.',
    'Internal IP' => 'IP interne',
    'Kernel version' => 'Version du noyau',
    'Key' => 'Clé',
    'Key Storage Items' => 'Articles de stockage de clés',
    'Key-Value Storage' => 'Stockage clé-valeur',
    'Language' => 'La langue',
    'Level' => 'Niveau',
    'Load average' => 'Charge moyenne',
    'Log Time' => 'Heure du journal',
    'Logout' => 'Connectez - Out',
    'Logs' => 'Les journaux',
    'Main' => 'Principale',
    'Male' => 'Mâle',
    'Member since {0, date, short}' => 'Membre depuis {0, date, court}',
    'Memory' => 'Mémoire',
    'Memory Usage' => 'Utilisation de la mémoire',
    'Message' => 'Message',
    'More info' => 'Plus d\'informations',
    'Network' => 'Réseau',
    'No events found' => 'Aucun événement trouvé',
    'Number of cores' => 'Nombres de coeurs',
    'OS' => 'OS',
    'OS Release' => 'Version de l\'OS',
    'Off' => 'De',
    'On' => 'Sur',
    'Operating System' => 'Système opérateur',
    'PHP Version' => 'Version PHP',
    'Pages' => 'Des pages',
    'Password' => 'Mot de passe',
    'Password Confirm' => 'Confirmer le mot de passe',
    'Port' => 'Port',
    'Prefix' => 'Préfixe',
    'Processor' => 'Processeur',
    'Processor Architecture' => 'Architecture du processeur',
    'Profile' => 'Profil',
    'Real time' => 'Temps réél',
    'Remember Me' => 'Se souvenir de moi',
    'Reset' => 'Réinitialiser',
    'Save' => 'sauvegarder',
    'Search' => 'Chercher',
    'Select cache' => 'Sélectionner le cache',
    'Settings was successfully saved' => 'Les paramètres ont été enregistrés avec succès',
    'Sign In' => 'Se connecter',
    'Software' => 'Logiciel',
    'Sorry, application failed to collect information about your system. See {link}.' => 'Désolé, l\'application n\'a pas réussi à collecter des informations sur votre système. Voir {link}.',
    'Static pages' => 'Pages statiques',
    'System' => 'Système',
    'System Date' => 'Date du système',
    'System Information' => 'Informations système',
    'System Logs' => 'Journaux système',
    'System Time' => 'Le temps du système',
    'Tag' => 'Étiquette',
    'TagDependency was invalidated' => 'TagDependency a été invalidé',
    'Text Blocks' => 'Blocs de texte',
    'This email has already been taken.' => 'Cet email a déjà été pris.',
    'This username has already been taken.' => 'Ce nom d\'utilisateur a déjà été pris.',
    'Time' => 'Temps',
    'Timeline' => 'Chronologie',
    'Timezone' => 'Fuseau horaire',
    'Total Swap' => 'Total Swap',
    'Total memory' => 'Mémoire totale',
    'Translation' => 'Traduction',
    'Update' => 'Mettre à jour',
    'Update {modelClass}: ' => 'Mettre à jour {modelClass}:',
    'Uptime' => 'La disponibilité',
    'Used size' => 'Utilisé taille',
    'User Registrations' => 'Inscriptions d\'utilisateurs',
    'Username' => 'Nom d\'utilisateur',
    'Users' => 'Utilisateurs',
    'View all' => 'Voir tout',
    'Web Server' => 'Serveur Web',
    'Widget Carousel Items' => 'Articles de carrousel de widget',
    'Widget Carousels' => 'Widget Carousels',
    'Widget Menus' => 'Widget Menus',
    'You have new event' => 'Vous avez un nouvel événement',
    'You have {num} log items' => 'Vous avez {num} éléments de journal',
    'Your account has been successfully saved' => 'Votre compte a été enregistré avec succès',
    'Your profile has been successfully saved' => 'Votre profil a été enregistré avec succès',
    'Add New {modelClass}' => '',
    'Assignments' => '',
    'Body small text' => '',
    'Brand small text' => '',
    'Cache Components' => '',
    'Cache elements' => '',
    'Carousel' => '',
    'Categories' => '',
    'Child' => '',
    'Child Items' => '',
    'Clear Logs' => '',
    'Collapsed sidebar' => '',
    'Create a new carousel' => '',
    'Create a new category' => '',
    'Create a new key storage item' => '',
    'Create a new menu' => '',
    'Create a new text block' => '',
    'Created At' => '',
    'Data' => '',
    'Description' => '',
    'Disable sidebar hover/focus auto expand' => '',
    'Files' => '',
    'Fixed footer' => '',
    'Fixed navbar' => '',
    'Fixed sidebar' => '',
    'Flush Cache' => '',
    'Footer small text' => '',
    'Go to logs' => '',
    'Go to timeline' => '',
    'Indent sidebar child menu items' => '',
    'Invalidate tag dependency' => '',
    'Item Name' => '',
    'Items' => '',
    'Login' => '',
    'Maintenance mode' => '',
    'Manager' => '',
    'Menu' => '',
    'Mini sidebar' => '',
    'Name' => '',
    'Navbar small text' => '',
    'New user! {identity} has signed up' => '',
    'No navbar border' => '',
    'Oops! Something went wrong... You may audit the error by reviewing the system logs or the application timeline.' => '',
    'Parent' => '',
    'Please select a child item...' => '',
    'Please select a parent item...' => '',
    'Please select a rule...' => '',
    'Please select a type...' => '',
    'Please select an item...' => '',
    'Please select an user...' => '',
    'RBAC Rules' => '',
    'Rule Name' => '',
    'Rules' => '',
    'Save Changes' => '',
    'Sidebar compact style' => '',
    'Sidebar flat style' => '',
    'Sidebar legacy style' => '',
    'Sidebar small text' => '',
    'Sign in to start your session' => '',
    'Storage' => '',
    'The logs have been cleared' => '',
    'Translations' => '',
    'Type' => '',
    'Updated At' => '',
    'Uploads' => '',
    'User' => '',
    'User ID' => '',
    'Widgets' => '',
    '{identity} has been deleted' => '',
];
