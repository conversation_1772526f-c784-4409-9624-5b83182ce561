{"name": "yii2-starter-kit/yii2-starter-kit", "description": "Yii2 Starter Kit Application Template", "keywords": ["yii2", "framework", "start", "cms", "application template", "yii2 advanced"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://vgr.cl"}], "type": "project", "license": "BSD-3-<PERSON><PERSON>", "support": {"issues": "https://github.com/yii2-starter-kit/yii2-starter-kit/issues?state=open", "source": "https://github.com/yii2-starter-kit/yii2-starter-kit"}, "require": {"php": ">=7.4 || ^8.0", "ext-intl": "*", "ext-gd": "*", "yiisoft/yii2": "^2.0.13", "yiisoft/yii2-swiftmailer": "^2.0.0", "yiisoft/yii2-authclient": "^2.0.0", "yiisoft/yii2-bootstrap4": "^2.0", "yiisoft/yii2-jui": "^2.0.0", "yiisoft/yii2-debug": "^2.0.0", "yii2-starter-kit/yii2-file-kit": "^2.1.0", "asofter/yii2-imperavi-redactor": ">=0.0.3@dev", "trntv/yii2-aceeditor": "^2.0", "trntv/probe": "^1.0", "trntv/yii2-glide": "^1.2", "trntv/yii2-datetime-widget": "dev-master@dev", "trntv/cheatsheet": "^0.1@dev", "trntv/yii2-command-bus": "^3.0", "intervention/image": "^2.1", "vlucas/phpdotenv": "^5.3.0", "almasaeed2010/adminlte": "^3.0", "npm-asset/font-awesome": "^5.0", "npm-asset/html5shiv": "^3.0", "npm-asset/jquery-slimscroll": "^1.3", "npm-asset/flot": "^3.2", "symfony/process": "^4.0", "guzzlehttp/guzzle": "^7.4", "alexantr/yii2-elfinder": "^1.3", "yii-starter-kit/sitemaped": "^2.0", "rmrevin/yii2-fontawesome": "^3.4", "kartik-v/yii2-widgets": "^3.4", "phpoffice/phpspreadsheet": "2.0.0"}, "require-dev": {"yiisoft/yii2-gii": "^2.0.0", "yiisoft/yii2-faker": "^2.0.0", "codeception/codeception": "^5.0", "codeception/verify": "^2.2", "codeception/module-asserts": "^3.0", "codeception/module-yii2": "^1.1", "codeception/module-phpbrowser": "^3.0"}, "autoload-dev": {"psr-4": {"tests\\": "tests/"}}, "suggest": {"trntv/yii2-debug-xhprof": "dev-master@dev"}, "config": {"process-timeout": 1800, "optimize-autoloader": true, "allow-plugins": {"yiisoft/yii2-composer": true}}, "repositories": [{"type": "composer", "url": "https://asset-packagist.org"}], "archive": {"exclude": ["docs"]}, "scripts": {"compile": ["cp deploy/heroku/.env .env", "YII_ENV=heroku php console/yii migrate/fresh --interactive=0", "php console/yii app/setup --interactive=0"], "build:env": ["cp .env.dist .env"], "build:app": ["@composer install --prefer-dist -o", "echo 'waiting for mysql' && sleep 10", "php console/yii app/setup --interactive=0"], "docker:build": ["@build:env", "@docker:start", "docker-compose exec -T console composer run-script build:app", "docker-compose run -T --rm node npm install", "docker-compose run -T --rm node npm run build"], "docker:start": ["docker-compose up --build -d"], "docker:cleanup": ["docker-compose rm -fsv"], "docker:tests": ["@docker:start", "docker-compose exec -T db mysql -uroot -proot -e \"CREATE DATABASE IF NOT EXISTS \\`yii2-starter-kit-test\\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci\"", "docker-compose exec -T console ./vendor/bin/codecept build", "docker-compose exec -T console php tests/bin/yii app/setup --interactive=0", "echo 'open a new terminal and run \"docker-compose exec -T console vendor/bin/codecept run\"' && docker-compose exec -T console php -S localhost:8080"]}}