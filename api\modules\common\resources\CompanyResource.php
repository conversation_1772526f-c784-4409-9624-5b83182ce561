<?php

namespace api\modules\common\resources;

use common\models\Company;

class CompanyResource extends Company
{
    public function fields(): array
    {
        return [
            'id',
            'logo',
            'name',
//            'summary',
//            'address',
        ];
    }

    public function getLogo()
    {
        return $this->hasOne(FileResource::class, ['id' => 'logo_id']);
    }
}