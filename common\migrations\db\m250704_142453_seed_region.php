<?php

use common\enums\RegionEnum;
use yii\db\Migration;

/**
 * Class m231202_142453_seed_region
 */
class m250704_142453_seed_region extends Migration
{
  /**
   * {@inheritdoc}
   */
  public function safeUp()
  {
      $regionList = [
      [
        "title_ru" => "Андижанская область",
        "title_uz" => "Andijon viloyati",
        "title_en" => "Andijan region",
      ],
      [
        "title_ru" => "Бухарская область",
        "title_uz" => "Buxoro viloyati",
        "title_en" => "Bukhara region",
      ],
      [
        "title_ru" => "Джизакская область",
        "title_uz" => "Jizzax viloyati",
        "title_en" => "Jizzakh region",
      ],
      [
        "title_ru" => "Кашкадарьинская область",
        "title_uz" => "Qashqadaryo viloyati",
        "title_en" => "Kashkadarya region",
      ],
      [
        "title_ru" => "Навоийская область",
        "title_uz" => "Navoiy viloyati",
        "title_en" => "Navoi region",
      ],
      [
        "title_ru" => "Наманганская область",
        "title_uz" => "Namangan viloyati",
        "title_en" => "Namangan region",
      ],
      [
        "title_ru" => "Самаркандская область",
        "title_uz" => "Samarqand viloyati",
        "title_en" => "Samarkand region",
      ],
      [
        "title_ru" => "Сурхандарьинская область",
        "title_uz" => "Surxondaryo viloyati",
        "title_en" => "Surkhandarya region",
      ],
      [
        "title_ru" => "Сырдарьинская область",
        "title_uz" => "Sirdaryo viloyati",
        "title_en" => "Syrdarya region",
      ],
      [
        "title_ru" => "Ташкентская область",
        "title_uz" => "Toshkent viloyati",
        "title_en" => "Tashkent region",
      ],
      [
        "title_ru" => "Ферганская область",
        "title_uz" => "Farg'ona viloyati",
        "title_en" => "Fergana region",
      ],
      [
        "title_ru" => "Хорезмская область",
        "title_uz" => "Xorazm viloyati",
        "title_en" => "Khorezm region",
      ],
      [
        "title_ru" => "Республика Каракалпакстан",
        "title_uz" => "Qoraqalpog'iston Respublikasi",
        "title_en" => "Republic of Karakalpakstan",
      ],
      [
        "title_ru" => "город Ташкент",
        "title_uz" => "Toshkent shahri",
        "title_en" => "Tashkent city",
      ],
    ];

    foreach ($regionList as $region) {
      $region['type'] = RegionEnum::TYPE_REGION;
      $this->insert('region', $region);
    }
  }

  /**
   * {@inheritdoc}
   */
  public function safeDown()
  {
     return false;
  }
}
