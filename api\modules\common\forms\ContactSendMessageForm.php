<?php

namespace api\modules\common\forms;

use common\base\BaseModel;
use common\components\services\BotService;

class ContactSendMessageForm extends BaseModel
{
    public ?string $firstname = null;
    public ?string $lastname = null;
    public ?string $phone_number = null;
    public ?string $message = null;

    public function __construct(
        public BotService $service,
        $config = [],
    )
    {
        parent::__construct($config);
    }

    public function rules(): array
    {
        return [
            [['firstname','lastname','phone_number','message'], 'required'],
            [['firstname','lastname','message'], 'trim'],
            [['firstname','lastname','message'], 'string','min' => 3, 'max' => 255],
            ['phone_number', 'match', 'pattern' => '/^\+998\s\d{2}\s\d{3}\s\d{2}\s\d{2}$/'],
        ];
    }

    public function getResult(): bool
    {
        return $this->service
            ->setChatID(env('TELEGRAM_CONTACT_GROUP_ID'))
            ->setMessage($this->getMessage())
            ->sendMessage();
    }

    private function getMessage(): string
    {
        $message  = "\n\n";
        $message .= "Сообщение \n\n";
        $message .= '👨‍💼 ' . $this->firstname . ' ' . $this->lastname . "\n";
        $message .= '📱 ' . $this->phone_number . "\n";
        $message .= '✍️ ' . $this->message . "\n";
        $message .= " \n\n";

        return $message;
    }
}