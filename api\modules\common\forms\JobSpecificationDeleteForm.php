<?php

namespace api\modules\common\forms;

use api\modules\common\resources\JobSpecificationResource;
use common\base\BaseModel;
use common\enums\StatusEnum;
use yii\db\StaleObjectException;

class JobSpecificationDeleteForm extends BaseModel
{
    public function __construct(
        public ?JobSpecificationResource $model = null,
        $config = [],
    )
    {
        parent::__construct($config);
    }

    /**
     * @throws StaleObjectException
     * @throws \Throwable
     */
    public function getResult(): bool|int
    {
        return $this->model->delete();
    }
}