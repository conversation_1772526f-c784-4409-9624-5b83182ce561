<?php

namespace api\modules\common\forms;

use api\modules\common\resources\PageResource;
use common\base\BaseModel;
use common\enums\PageEnum;
use common\enums\StatusEnum;

class PageForm extends BaseModel
{
    public ?string $title = null;
    public ?string $body = null;
    public ?string $type = null;
    public ?string $status = null;
    public ?string $published_at = null;

    public function rules(): array
    {
        return [
            [['title', 'body', 'type', 'status', 'published_at'], 'required'],
            [['title','body'], 'trim'],
            ['title', 'string', 'min' => 2, 'max' => 255],
            ['body', 'string'],
            [['type', 'status',], 'integer'],
            ['type', 'in', 'range' => PageEnum::TYPE_LIST],
            ['status', 'in', 'range' => StatusEnum::STATUS_LIST],
            ['published_at', 'date', 'format' => 'php:d.m.Y'],
        ];
    }

    public function __construct(
        public ?PageResource $model = null,
        $config = [],
    )
    {
        parent::__construct($config);
    }

    public function getResult(): bool
    {
        $this->model->attributes = [
            'title' => $this->title,
            'body' => $this->body,
            'type' => $this->type,
            'status' => $this->status,
            'published_at' => $this->published_at ? strtotime($this->published_at) : null,
        ];

        if (!$this->model->save()) {
            $this->addErrors($this->model->errors);
            return false;
        }

        return true;
    }
}