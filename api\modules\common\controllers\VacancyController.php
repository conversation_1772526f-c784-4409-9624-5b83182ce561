<?php

namespace api\modules\common\controllers;

use api\modules\common\filters\VacancyFilter;
use api\modules\common\forms\VacancyDeleteForm;
use Yii;
use api\modules\common\forms\VacancyUpdateForm;
use api\modules\common\forms\VacancyForm;
use api\modules\common\resources\VacancyResource;
use common\base\BaseController;
use yii\web\NotFoundHttpException;

class VacancyController extends BaseController
{
    public function actionIndex(): array
    {
        return $this->sendResponse(
            new VacancyFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionCreate(): array
    {
        return $this->sendResponse(
            new VacancyForm(new VacancyResource()),
            Yii::$app->request->bodyParams
        );
    }

    /**
     * @throws NotFoundHttpException
     */
    public function actionUpdate($id): array
    {
        return $this->sendResponse(
            new VacancyUpdateForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }

    /**
     * @throws NotFoundHttpException
     */
    public function actionView($id): array
    {
        return $this->sendModel($this->findOne($id));
    }

    /**
     * @throws NotFoundHttpException
     */
    public function actionDelete($id): array
    {
        return $this->sendResponse(
            new VacancyDeleteForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }

    /**
     * @throws NotFoundHttpException
     */
    protected function findOne($id): VacancyResource
    {
        $model = VacancyResource::findOne(['id' => $id, 'deleted_at' => null]);
        if ($model !== null) {
            return $model;
        }
        throw new NotFoundHttpException('Model does not exist.');
    }
}