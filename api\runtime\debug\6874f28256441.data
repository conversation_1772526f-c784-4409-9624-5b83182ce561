a:14:{s:6:"config";s:8705:"a:5:{s:10:"phpVersion";s:6:"8.0.30";s:10:"yiiVersion";s:6:"2.0.46";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.46";s:4:"name";s:16:"Yii2 Starter Kit";s:7:"version";s:3:"1.0";s:8:"language";s:2:"uz";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";s:4:"true";}s:3:"php";a:5:{s:7:"version";s:6:"8.0.30";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:37:{s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:7:"2.0.7.0";s:5:"alias";a:1:{s:8:"@yii/jui";s:28:"/app/vendor/yiisoft/yii2-jui";}}s:22:"alexantr/yii2-elfinder";a:3:{s:4:"name";s:22:"alexantr/yii2-elfinder";s:7:"version";s:7:"1.8.0.0";s:5:"alias";a:1:{s:18:"@alexantr/elfinder";s:38:"/app/vendor/alexantr/yii2-elfinder/src";}}s:30:"asofter/yii2-imperavi-redactor";a:3:{s:4:"name";s:30:"asofter/yii2-imperavi-redactor";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:13:"@yii/imperavi";s:55:"/app/vendor/asofter/yii2-imperavi-redactor/yii/imperavi";}}s:30:"kartik-v/yii2-widget-typeahead";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-typeahead";s:7:"version";s:7:"1.0.4.0";s:5:"alias";a:1:{s:17:"@kartik/typeahead";s:46:"/app/vendor/kartik-v/yii2-widget-typeahead/src";}}s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:7:"3.0.5.0";s:5:"alias";a:1:{s:12:"@kartik/base";s:41:"/app/vendor/kartik-v/yii2-krajee-base/src";}}s:30:"kartik-v/yii2-widget-touchspin";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-touchspin";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:17:"@kartik/touchspin";s:46:"/app/vendor/kartik-v/yii2-widget-touchspin/src";}}s:31:"kartik-v/yii2-widget-timepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-timepicker";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:12:"@kartik/time";s:47:"/app/vendor/kartik-v/yii2-widget-timepicker/src";}}s:32:"kartik-v/yii2-widget-switchinput";a:3:{s:4:"name";s:32:"kartik-v/yii2-widget-switchinput";s:7:"version";s:7:"1.3.1.0";s:5:"alias";a:1:{s:19:"@kartik/switchinput";s:44:"/app/vendor/kartik-v/yii2-widget-switchinput";}}s:28:"kartik-v/yii2-widget-spinner";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-spinner";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/spinner";s:44:"/app/vendor/kartik-v/yii2-widget-spinner/src";}}s:28:"kartik-v/yii2-widget-sidenav";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-sidenav";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/sidenav";s:40:"/app/vendor/kartik-v/yii2-widget-sidenav";}}s:27:"kartik-v/yii2-widget-rating";a:3:{s:4:"name";s:27:"kartik-v/yii2-widget-rating";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:14:"@kartik/rating";s:43:"/app/vendor/kartik-v/yii2-widget-rating/src";}}s:31:"kartik-v/yii2-widget-rangeinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-rangeinput";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:13:"@kartik/range";s:47:"/app/vendor/kartik-v/yii2-widget-rangeinput/src";}}s:26:"kartik-v/yii2-widget-growl";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-growl";s:7:"version";s:7:"1.1.2.0";s:5:"alias";a:1:{s:13:"@kartik/growl";s:42:"/app/vendor/kartik-v/yii2-widget-growl/src";}}s:28:"kartik-v/yii2-widget-select2";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-select2";s:7:"version";s:7:"2.2.4.0";s:5:"alias";a:1:{s:15:"@kartik/select2";s:44:"/app/vendor/kartik-v/yii2-widget-select2/src";}}s:28:"kartik-v/yii2-widget-depdrop";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-depdrop";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:15:"@kartik/depdrop";s:44:"/app/vendor/kartik-v/yii2-widget-depdrop/src";}}s:30:"kartik-v/yii2-widget-fileinput";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-fileinput";s:7:"version";s:7:"1.1.1.0";s:5:"alias";a:1:{s:12:"@kartik/file";s:46:"/app/vendor/kartik-v/yii2-widget-fileinput/src";}}s:26:"kartik-v/yii2-widget-alert";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-alert";s:7:"version";s:7:"1.1.5.0";s:5:"alias";a:1:{s:13:"@kartik/alert";s:42:"/app/vendor/kartik-v/yii2-widget-alert/src";}}s:31:"kartik-v/yii2-widget-colorinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-colorinput";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:13:"@kartik/color";s:47:"/app/vendor/kartik-v/yii2-widget-colorinput/src";}}s:26:"kartik-v/yii2-widget-affix";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-affix";s:7:"version";s:7:"1.0.0.0";s:5:"alias";a:1:{s:13:"@kartik/affix";s:38:"/app/vendor/kartik-v/yii2-widget-affix";}}s:31:"kartik-v/yii2-widget-activeform";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-activeform";s:7:"version";s:7:"1.6.2.0";s:5:"alias";a:1:{s:12:"@kartik/form";s:47:"/app/vendor/kartik-v/yii2-widget-activeform/src";}}s:21:"kartik-v/yii2-widgets";a:3:{s:4:"name";s:21:"kartik-v/yii2-widgets";s:7:"version";s:7:"3.4.1.0";s:5:"alias";a:1:{s:15:"@kartik/widgets";s:37:"/app/vendor/kartik-v/yii2-widgets/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:7:"1.5.1.0";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:51:"/app/vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:31:"kartik-v/yii2-widget-datepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-datepicker";s:7:"version";s:7:"1.4.8.0";s:5:"alias";a:1:{s:12:"@kartik/date";s:47:"/app/vendor/kartik-v/yii2-widget-datepicker/src";}}s:20:"trntv/yii2-aceeditor";a:3:{s:4:"name";s:20:"trntv/yii2-aceeditor";s:7:"version";s:7:"2.1.2.0";s:5:"alias";a:1:{s:16:"@trntv/aceeditor";s:36:"/app/vendor/trntv/yii2-aceeditor/src";}}s:22:"trntv/yii2-command-bus";a:3:{s:4:"name";s:22:"trntv/yii2-command-bus";s:7:"version";s:7:"3.2.0.0";s:5:"alias";a:1:{s:10:"@trntv/bus";s:38:"/app/vendor/trntv/yii2-command-bus/src";}}s:18:"yiisoft/yii2-queue";a:3:{s:4:"name";s:18:"yiisoft/yii2-queue";s:7:"version";s:7:"2.3.4.0";s:5:"alias";a:11:{s:10:"@yii/queue";s:34:"/app/vendor/yiisoft/yii2-queue/src";s:13:"@yii/queue/db";s:45:"/app/vendor/yiisoft/yii2-queue/src/drivers/db";s:14:"@yii/queue/sqs";s:46:"/app/vendor/yiisoft/yii2-queue/src/drivers/sqs";s:15:"@yii/queue/amqp";s:47:"/app/vendor/yiisoft/yii2-queue/src/drivers/amqp";s:15:"@yii/queue/file";s:47:"/app/vendor/yiisoft/yii2-queue/src/drivers/file";s:15:"@yii/queue/sync";s:47:"/app/vendor/yiisoft/yii2-queue/src/drivers/sync";s:16:"@yii/queue/redis";s:48:"/app/vendor/yiisoft/yii2-queue/src/drivers/redis";s:16:"@yii/queue/stomp";s:48:"/app/vendor/yiisoft/yii2-queue/src/drivers/stomp";s:18:"@yii/queue/gearman";s:50:"/app/vendor/yiisoft/yii2-queue/src/drivers/gearman";s:20:"@yii/queue/beanstalk";s:52:"/app/vendor/yiisoft/yii2-queue/src/drivers/beanstalk";s:23:"@yii/queue/amqp_interop";s:55:"/app/vendor/yiisoft/yii2-queue/src/drivers/amqp_interop";}}s:26:"trntv/yii2-datetime-widget";a:3:{s:4:"name";s:26:"trntv/yii2-datetime-widget";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:19:"@trntv/yii/datetime";s:42:"/app/vendor/trntv/yii2-datetime-widget/src";}}s:16:"trntv/yii2-glide";a:3:{s:4:"name";s:16:"trntv/yii2-glide";s:7:"version";s:7:"1.2.2.0";s:5:"alias";a:1:{s:12:"@trntv/glide";s:32:"/app/vendor/trntv/yii2-glide/src";}}s:24:"rmrevin/yii2-fontawesome";a:3:{s:4:"name";s:24:"rmrevin/yii2-fontawesome";s:7:"version";s:7:"3.7.0.0";s:5:"alias";a:1:{s:24:"@rmrevin/yii/fontawesome";s:36:"/app/vendor/rmrevin/yii2-fontawesome";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"2.0.14.0";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:39:"/app/vendor/yiisoft/yii2-httpclient/src";}}s:30:"yii2-starter-kit/yii2-file-kit";a:3:{s:4:"name";s:30:"yii2-starter-kit/yii2-file-kit";s:7:"version";s:7:"2.1.5.0";s:5:"alias";a:1:{s:14:"@trntv/filekit";s:46:"/app/vendor/yii2-starter-kit/yii2-file-kit/src";}}s:23:"yiisoft/yii2-authclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-authclient";s:7:"version";s:8:"2.2.13.0";s:5:"alias";a:1:{s:15:"@yii/authclient";s:39:"/app/vendor/yiisoft/yii2-authclient/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.10.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:39:"/app/vendor/yiisoft/yii2-bootstrap4/src";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/faker";s:34:"/app/vendor/yiisoft/yii2-faker/src";}}s:24:"yiisoft/yii2-swiftmailer";a:3:{s:4:"name";s:24:"yiisoft/yii2-swiftmailer";s:7:"version";s:7:"2.1.3.0";s:5:"alias";a:1:{s:16:"@yii/swiftmailer";s:40:"/app/vendor/yiisoft/yii2-swiftmailer/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.5.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:32:"/app/vendor/yiisoft/yii2-gii/src";}}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.21.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:34:"/app/vendor/yiisoft/yii2-debug/src";}}}}";s:3:"log";s:24241:"a:1:{s:8:"messages";a:42:{i:0;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752494721.682031;i:4;a:0:{}i:5;i:1133776;}i:1;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1752494721.68227;i:4;a:0:{}i:5;i:1134952;}i:2;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752494721.722762;i:4;a:0:{}i:5;i:1140512;}i:3;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1752494721.793422;i:4;a:0:{}i:5;i:1228864;}i:4;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:1752494722.217583;i:4;a:0:{}i:5;i:1399512;}i:5;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752494722.34083;i:4;a:0:{}i:5;i:1417328;}i:12;a:6:{i:0;s:36:"Route requested: 'common/main/index'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:1752494722.398661;i:4;a:0:{}i:5;i:1514920;}i:13;a:6:{i:0;s:22:"Loading module: common";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1752494722.398718;i:4;a:0:{}i:5;i:1516544;}i:14;a:6:{i:0;s:31:"Route to run: common/main/index";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:1752494722.480201;i:4;a:0:{}i:5;i:1548592;}i:15;a:6:{i:0;s:39:"Rate limit skipped: user not logged in.";i:1;i:4;i:2;s:37:"yii\filters\RateLimiter::beforeAction";i:3;d:1752494722.572453;i:4;a:0:{}i:5;i:1582984;}i:16;a:6:{i:0;s:76:"Running action: api\modules\common\controllers\MainController::actionIndex()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:1752494722.572639;i:4;a:0:{}i:5;i:1581832;}i:17;a:6:{i:0;s:256:"SELECT COUNT(*) FROM (SELECT "js"."id", "js"."name_uz" AS "name_uz", "js"."icon_id", COUNT(v.category_type_id) AS "vacancy_count" FROM "job_specification" "js" LEFT JOIN "vacancy" "v" ON v.category_type_id = js.id GROUP BY "js"."id" ORDER BY "js"."id") "c"";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752494722.89028;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:120;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:1969688;}i:18;a:6:{i:0;s:65:"Opening DB connection: pgsql:host=db;port=5432;dbname=skillhub_db";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1752494722.890858;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:120;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:1972880;}i:23;a:6:{i:0;s:238:"SELECT "js"."id", "js"."name_uz" AS "name_uz", "js"."icon_id", COUNT(v.category_type_id) AS "vacancy_count" FROM "job_specification" "js" LEFT JOIN "vacancy" "v" ON v.category_type_id = js.id GROUP BY "js"."id" ORDER BY "js"."id" LIMIT 10";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752494722.954449;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:123;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:1981920;}i:26;a:6:{i:0;s:2823:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'job_specification'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.004998;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:123;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:2014232;}i:29;a:6:{i:0;s:885:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='job_specification'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.073318;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:123;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:2033880;}i:32;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.249611;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2184256;}i:35;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.251249;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2190144;}i:38;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.332011;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2221544;}i:41;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=2";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.341115;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2229232;}i:44;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=2";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.342252;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2233840;}i:47;a:6:{i:0;s:33:"SELECT * FROM "file" WHERE "id"=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.343038;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2238888;}i:50;a:6:{i:0;s:2810:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'file'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.344016;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2256424;}i:53;a:6:{i:0;s:872:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='file'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.356165;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2267816;}i:56;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=3";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.359992;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2274944;}i:59;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=3";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.361153;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2279552;}i:62;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.361916;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2283320;}i:65;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=4";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.362549;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2296264;}i:68;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=4";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.36344;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2300872;}i:71;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.364441;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2304640;}i:74;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=5";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.365248;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2311952;}i:77;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=5";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.366202;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2316560;}i:80;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.367842;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2320328;}i:83;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=6";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.369034;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2327640;}i:86;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=6";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.371101;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2332248;}i:89;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.372282;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2336016;}i:92;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.373653;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2343328;}i:95;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.374679;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2347936;}i:98;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.37577;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2351704;}i:101;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=8";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.377256;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2359016;}i:104;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=8";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.378975;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2363624;}i:107;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.379814;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2367392;}}}";s:9:"profiling";s:45175:"a:3:{s:6:"memory";i:2409656;s:4:"time";d:2.2841060161590576;s:8:"messages";a:62:{i:19;a:6:{i:0;s:65:"Opening DB connection: pgsql:host=db;port=5432;dbname=skillhub_db";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1752494722.890894;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:120;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:1974760;}i:20;a:6:{i:0;s:65:"Opening DB connection: pgsql:host=db;port=5432;dbname=skillhub_db";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1752494722.928564;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:120;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:1977504;}i:21;a:6:{i:0;s:256:"SELECT COUNT(*) FROM (SELECT "js"."id", "js"."name_uz" AS "name_uz", "js"."icon_id", COUNT(v.category_type_id) AS "vacancy_count" FROM "job_specification" "js" LEFT JOIN "vacancy" "v" ON v.category_type_id = js.id GROUP BY "js"."id" ORDER BY "js"."id") "c"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494722.929216;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:120;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:1979576;}i:22;a:6:{i:0;s:256:"SELECT COUNT(*) FROM (SELECT "js"."id", "js"."name_uz" AS "name_uz", "js"."icon_id", COUNT(v.category_type_id) AS "vacancy_count" FROM "job_specification" "js" LEFT JOIN "vacancy" "v" ON v.category_type_id = js.id GROUP BY "js"."id" ORDER BY "js"."id") "c"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494722.953768;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:120;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:1981520;}i:24;a:6:{i:0;s:238:"SELECT "js"."id", "js"."name_uz" AS "name_uz", "js"."icon_id", COUNT(v.category_type_id) AS "vacancy_count" FROM "job_specification" "js" LEFT JOIN "vacancy" "v" ON v.category_type_id = js.id GROUP BY "js"."id" ORDER BY "js"."id" LIMIT 10";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494722.954486;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:123;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:1984352;}i:25;a:6:{i:0;s:238:"SELECT "js"."id", "js"."name_uz" AS "name_uz", "js"."icon_id", COUNT(v.category_type_id) AS "vacancy_count" FROM "job_specification" "js" LEFT JOIN "vacancy" "v" ON v.category_type_id = js.id GROUP BY "js"."id" ORDER BY "js"."id" LIMIT 10";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494722.957229;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:123;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:1990192;}i:27;a:6:{i:0;s:2823:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'job_specification'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.005063;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:123;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:2025688;}i:28;a:6:{i:0;s:2823:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'job_specification'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.047179;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:123;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:2050168;}i:30;a:6:{i:0;s:885:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='job_specification'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.073371;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:123;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:2038936;}i:31;a:6:{i:0;s:885:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='job_specification'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.084306;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:123;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:2042136;}i:33;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.249708;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2188424;}i:34;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.250728;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2190032;}i:36;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.251293;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2193032;}i:37;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.252065;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2194640;}i:39;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.332065;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2223152;}i:40;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.339165;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2225192;}i:42;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=2";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.341157;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2232120;}i:43;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=2";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.341895;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2233728;}i:45;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=2";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.342277;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2236728;}i:46;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=2";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.342707;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2238336;}i:48;a:6:{i:0;s:33:"SELECT * FROM "file" WHERE "id"=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.343073;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2241320;}i:49;a:6:{i:0;s:33:"SELECT * FROM "file" WHERE "id"=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.343714;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2244168;}i:51;a:6:{i:0;s:2810:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'file'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.344126;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2267128;}i:52;a:6:{i:0;s:2810:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'file'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.355491;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2290616;}i:54;a:6:{i:0;s:872:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='file'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.356198;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2272120;}i:55;a:6:{i:0;s:872:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='file'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.359445;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2273688;}i:57;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=3";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.360156;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2277832;}i:58;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=3";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.360871;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2279440;}i:60;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=3";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.361174;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2282440;}i:61;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=3";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.361565;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2284048;}i:63;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.361933;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2284928;}i:64;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.362277;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2286968;}i:66;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.362566;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2299152;}i:67;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.363078;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2300760;}i:69;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.36347;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2303760;}i:70;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.364181;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2305368;}i:72;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.36462;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2306248;}i:73;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.365013;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2308288;}i:75;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=5";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.365264;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2314840;}i:76;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=5";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.365809;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2316448;}i:78;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=5";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.366337;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2319448;}i:79;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=5";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.367424;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2321056;}i:81;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.367866;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2321936;}i:82;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.368522;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2323976;}i:84;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=6";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.369076;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2330528;}i:85;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=6";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.370366;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2332136;}i:87;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=6";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.371137;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2335136;}i:88;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=6";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.371891;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2336744;}i:90;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.372362;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2337624;}i:91;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.37321;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2339664;}i:93;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.373789;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2346216;}i:94;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.374337;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2347824;}i:96;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.374704;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2350824;}i:97;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.375416;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2352432;}i:99;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.375794;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2353312;}i:100;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.376778;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2355352;}i:102;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=8";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.377503;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2361904;}i:103;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=8";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.378549;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2363512;}i:105;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=8";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.379002;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2366512;}i:106;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=8";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.379495;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2368120;}i:108;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.379831;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2369000;}i:109;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.380168;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2371040;}}}";s:2:"db";s:43846:"a:1:{s:8:"messages";a:60:{i:21;a:6:{i:0;s:256:"SELECT COUNT(*) FROM (SELECT "js"."id", "js"."name_uz" AS "name_uz", "js"."icon_id", COUNT(v.category_type_id) AS "vacancy_count" FROM "job_specification" "js" LEFT JOIN "vacancy" "v" ON v.category_type_id = js.id GROUP BY "js"."id" ORDER BY "js"."id") "c"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494722.929216;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:120;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:1979576;}i:22;a:6:{i:0;s:256:"SELECT COUNT(*) FROM (SELECT "js"."id", "js"."name_uz" AS "name_uz", "js"."icon_id", COUNT(v.category_type_id) AS "vacancy_count" FROM "job_specification" "js" LEFT JOIN "vacancy" "v" ON v.category_type_id = js.id GROUP BY "js"."id" ORDER BY "js"."id") "c"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494722.953768;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:120;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:1981520;}i:24;a:6:{i:0;s:238:"SELECT "js"."id", "js"."name_uz" AS "name_uz", "js"."icon_id", COUNT(v.category_type_id) AS "vacancy_count" FROM "job_specification" "js" LEFT JOIN "vacancy" "v" ON v.category_type_id = js.id GROUP BY "js"."id" ORDER BY "js"."id" LIMIT 10";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494722.954486;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:123;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:1984352;}i:25;a:6:{i:0;s:238:"SELECT "js"."id", "js"."name_uz" AS "name_uz", "js"."icon_id", COUNT(v.category_type_id) AS "vacancy_count" FROM "job_specification" "js" LEFT JOIN "vacancy" "v" ON v.category_type_id = js.id GROUP BY "js"."id" ORDER BY "js"."id" LIMIT 10";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494722.957229;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:123;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:1990192;}i:27;a:6:{i:0;s:2823:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'job_specification'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.005063;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:123;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:2025688;}i:28;a:6:{i:0;s:2823:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'job_specification'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.047179;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:123;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:2050168;}i:30;a:6:{i:0;s:885:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='job_specification'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.073371;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:123;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:2038936;}i:31;a:6:{i:0;s:885:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='job_specification'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.084306;i:4;a:3:{i:0;a:5:{s:4:"file";s:23:"/app/common/helpers.php";s:4:"line";i:123;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"/app/api/modules/common/filters/MainFilter.php";s:4:"line";i:22;s:8:"function";s:8:"paginate";}i:2;a:5:{s:4:"file";s:35:"/app/common/base/BaseController.php";s:4:"line";i:73;s:8:"function";s:9:"getResult";s:5:"class";s:37:"api\modules\common\filters\MainFilter";s:4:"type";s:2:"->";}}i:5;i:2042136;}i:33;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.249708;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2188424;}i:34;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.250728;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2190032;}i:36;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.251293;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2193032;}i:37;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.252065;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2194640;}i:39;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.332065;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2223152;}i:40;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.339165;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2225192;}i:42;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=2";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.341157;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2232120;}i:43;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=2";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.341895;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2233728;}i:45;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=2";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.342277;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2236728;}i:46;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=2";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.342707;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2238336;}i:48;a:6:{i:0;s:33:"SELECT * FROM "file" WHERE "id"=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.343073;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2241320;}i:49;a:6:{i:0;s:33:"SELECT * FROM "file" WHERE "id"=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.343714;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2244168;}i:51;a:6:{i:0;s:2810:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'file'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.344126;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2267128;}i:52;a:6:{i:0;s:2810:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'file'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.355491;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2290616;}i:54;a:6:{i:0;s:872:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='file'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.356198;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2272120;}i:55;a:6:{i:0;s:872:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='file'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.359445;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2273688;}i:57;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=3";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.360156;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2277832;}i:58;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=3";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.360871;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2279440;}i:60;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=3";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.361174;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2282440;}i:61;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=3";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.361565;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2284048;}i:63;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.361933;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2284928;}i:64;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.362277;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2286968;}i:66;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.362566;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2299152;}i:67;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.363078;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2300760;}i:69;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.36347;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2303760;}i:70;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.364181;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2305368;}i:72;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.36462;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2306248;}i:73;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.365013;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2308288;}i:75;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=5";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.365264;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2314840;}i:76;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=5";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.365809;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2316448;}i:78;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=5";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.366337;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2319448;}i:79;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=5";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.367424;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2321056;}i:81;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.367866;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2321936;}i:82;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.368522;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2323976;}i:84;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=6";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.369076;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2330528;}i:85;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=6";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.370366;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2332136;}i:87;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=6";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.371137;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2335136;}i:88;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=6";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.371891;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2336744;}i:90;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.372362;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2337624;}i:91;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.37321;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2339664;}i:93;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.373789;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2346216;}i:94;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.374337;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2347824;}i:96;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.374704;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2350824;}i:97;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.375416;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2352432;}i:99;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.375794;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2353312;}i:100;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.376778;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2355352;}i:102;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=8";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.377503;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2361904;}i:103;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=8";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.378549;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2363512;}i:105;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=8";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.379002;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2366512;}i:106;a:6:{i:0;s:57:"SELECT COUNT(*) FROM "vacancy" WHERE "category_type_id"=8";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.379495;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:28;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:19;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2368120;}i:108;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.379831;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2369000;}i:109;a:6:{i:0;s:30:"SELECT * FROM "file" WHERE 0=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752494723.380168;i:4;a:1:{i:0;a:5:{s:4:"file";s:50:"/app/api/modules/common/resources/MainResource.php";s:4:"line";i:22;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:2371040;}}}";s:5:"event";s:11614:"a:64:{i:0;a:5:{s:4:"time";d:1752494722.39786;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:1;a:5:{s:4:"time";d:1752494722.486862;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:2;a:5:{s:4:"time";d:1752494722.486908;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"api\modules\common\Module";}i:3;a:5:{s:4:"time";d:1752494722.572566;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:45:"api\modules\common\controllers\MainController";}i:4;a:5:{s:4:"time";d:1752494722.646222;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"api\modules\common\filters\MainFilter";}i:5;a:5:{s:4:"time";d:1752494722.64691;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"api\modules\common\filters\MainFilter";}i:6;a:5:{s:4:"time";d:1752494722.788952;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:7;a:5:{s:4:"time";d:1752494722.92855;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:8;a:5:{s:4:"time";d:1752494722.995887;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"api\modules\common\resources\MainResource";}i:9;a:5:{s:4:"time";d:1752494723.085042;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"api\modules\common\resources\MainResource";}i:10;a:5:{s:4:"time";d:1752494723.085276;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"api\modules\common\resources\MainResource";}i:11;a:5:{s:4:"time";d:1752494723.085393;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"api\modules\common\resources\MainResource";}i:12;a:5:{s:4:"time";d:1752494723.085418;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"api\modules\common\resources\MainResource";}i:13;a:5:{s:4:"time";d:1752494723.085463;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"api\modules\common\resources\MainResource";}i:14;a:5:{s:4:"time";d:1752494723.085672;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"api\modules\common\resources\MainResource";}i:15;a:5:{s:4:"time";d:1752494723.085727;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"api\modules\common\resources\MainResource";}i:16;a:5:{s:4:"time";d:1752494723.085774;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"api\modules\common\resources\MainResource";}i:17;a:5:{s:4:"time";d:1752494723.08578;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"api\modules\common\resources\MainResource";}i:18;a:5:{s:4:"time";d:1752494723.085784;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"api\modules\common\resources\MainResource";}i:19;a:5:{s:4:"time";d:1752494723.085788;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"api\modules\common\resources\MainResource";}i:20;a:5:{s:4:"time";d:1752494723.085792;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"api\modules\common\resources\MainResource";}i:21;a:5:{s:4:"time";d:1752494723.085796;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"api\modules\common\resources\MainResource";}i:22;a:5:{s:4:"time";d:1752494723.085801;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"api\modules\common\resources\MainResource";}i:23;a:5:{s:4:"time";d:1752494723.085804;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"api\modules\common\resources\MainResource";}i:24;a:5:{s:4:"time";d:1752494723.086077;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:45:"api\modules\common\controllers\MainController";}i:25;a:5:{s:4:"time";d:1752494723.120571;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:26;a:5:{s:4:"time";d:1752494723.251137;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:27;a:5:{s:4:"time";d:1752494723.331311;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:28;a:5:{s:4:"time";d:1752494723.340996;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:29;a:5:{s:4:"time";d:1752494723.342176;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:30;a:5:{s:4:"time";d:1752494723.342927;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:31;a:5:{s:4:"time";d:1752494723.343969;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"api\modules\common\resources\FileResource";}i:32;a:5:{s:4:"time";d:1752494723.359805;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"api\modules\common\resources\FileResource";}i:33;a:5:{s:4:"time";d:1752494723.359905;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:34;a:5:{s:4:"time";d:1752494723.361079;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:35;a:5:{s:4:"time";d:1752494723.361861;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:36;a:5:{s:4:"time";d:1752494723.362502;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:37;a:5:{s:4:"time";d:1752494723.363351;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:38;a:5:{s:4:"time";d:1752494723.364383;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:39;a:5:{s:4:"time";d:1752494723.365197;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:40;a:5:{s:4:"time";d:1752494723.36609;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:41;a:5:{s:4:"time";d:1752494723.367772;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:42;a:5:{s:4:"time";d:1752494723.368923;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:43;a:5:{s:4:"time";d:1752494723.370801;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:44;a:5:{s:4:"time";d:1752494723.37219;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:45;a:5:{s:4:"time";d:1752494723.373559;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:46;a:5:{s:4:"time";d:1752494723.374576;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:47;a:5:{s:4:"time";d:1752494723.375696;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:48;a:5:{s:4:"time";d:1752494723.377158;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:49;a:5:{s:4:"time";d:1752494723.378884;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:50;a:5:{s:4:"time";d:1752494723.379758;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:51;a:5:{s:4:"time";d:1752494723.380325;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"api\modules\common\Module";}i:52;a:5:{s:4:"time";d:1752494723.380332;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:53;a:5:{s:4:"time";d:1752494723.380339;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:54;a:5:{s:4:"time";d:1752494723.380351;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:55;a:5:{s:4:"time";d:1752494723.494145;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:56;a:5:{s:4:"time";d:1752494723.494275;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:57;a:5:{s:4:"time";d:1752494723.494282;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:58;a:5:{s:4:"time";d:1752494723.494287;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:59;a:5:{s:4:"time";d:1752494723.49429;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:60;a:5:{s:4:"time";d:1752494723.494294;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:61;a:5:{s:4:"time";d:1752494723.494298;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:62;a:5:{s:4:"time";d:1752494723.508985;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:63;a:5:{s:4:"time";d:1752494723.509589;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:90:"a:3:{s:5:"start";d:1752494721.248995;s:3:"end";d:1752494723.53348;s:6:"memory";i:2409656;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:1276:"a:3:{s:8:"messages";a:6:{i:6;a:6:{i:0;a:3:{s:4:"rule";s:5:"debug";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752494722.398171;i:4;a:0:{}i:5;i:1511472;}i:7;a:6:{i:0;a:3:{s:4:"rule";s:43:"debug/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752494722.398404;i:4;a:0:{}i:5;i:1512224;}i:8;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752494722.398413;i:4;a:0:{}i:5;i:1512976;}i:9;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752494722.398448;i:4;a:0:{}i:5;i:1514048;}i:10;a:6:{i:0;a:3:{s:4:"rule";s:41:"gii/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752494722.398454;i:4;a:0:{}i:5;i:1514800;}i:11;a:6:{i:0;s:55:"No matching URL rules. Using default URL parsing logic.";i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752494722.398491;i:4;a:0:{}i:5;i:1515176;}}s:5:"route";s:17:"common/main/index";s:6:"action";s:60:"api\modules\common\controllers\MainController::actionIndex()";}";s:7:"request";s:5377:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:10:{s:6:"cookie";s:42:"PHPSESSID=f1392907cd41fdc24dd47eb21ec2c6b6";s:10:"connection";s:10:"keep-alive";s:15:"accept-encoding";s:17:"gzip, deflate, br";s:4:"host";s:17:"api.skill-hub.loc";s:13:"postman-token";s:36:"4231e468-9ee8-48ff-ad1c-4f33438dacd9";s:13:"cache-control";s:8:"no-cache";s:6:"accept";s:3:"*/*";s:10:"user-agent";s:21:"PostmanRuntime/7.44.1";s:14:"content-length";s:0:"";s:12:"content-type";s:0:"";}s:15:"responseHeaders";a:10:{s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:32:"Access-Control-Allow-Credentials";s:4:"true";s:4:"Vary";s:15:"Accept-Language";s:15:"X-Frame-Options";s:4:"DENY";s:12:"Content-Type";s:31:"application/json; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"6874f28256441";s:16:"X-Debug-Duration";s:5:"2,261";s:12:"X-Debug-Link";s:37:"/debug/default/view?tag=6874f28256441";}s:5:"route";s:17:"common/main/index";s:6:"action";s:60:"api\modules\common\controllers\MainController::actionIndex()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:3:"GET";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:0:{}s:6:"SERVER";a:82:{s:17:"PHP_ENABLE_XDEBUG";s:1:"0";s:11:"PHP_USER_ID";s:2:"33";s:8:"HOSTNAME";s:12:"e2c384a79b49";s:11:"PHP_VERSION";s:6:"8.0.30";s:11:"PHP_INI_DIR";s:18:"/usr/local/etc/php";s:8:"GPG_KEYS";s:163:"1729F83938DA44E27BA0F4D3DBDB397470D12172 BFDDD28642824F8118EF77909B67A5C12229118F 2C16C765DBE54A088130F1BC4B9B5F600B55F3B4 39B641343D8C104B2B146DC3F9C39DC0B9698544";s:11:"PHP_LDFLAGS";s:12:"-Wl,-O1 -pie";s:3:"PWD";s:4:"/app";s:4:"HOME";s:8:"/var/www";s:10:"PHP_SHA256";s:64:"216ab305737a5d392107112d618a755dc5df42058226f1670e9db90e77d777d9";s:11:"PHPIZE_DEPS";s:76:"autoconf 		dpkg-dev 		file 		g++ 		gcc 		libc-dev 		make 		pkg-config 		re2c";s:4:"TERM";s:5:"linux";s:7:"PHP_URL";s:51:"https://www.php.net/distributions/php-8.0.30.tar.xz";s:5:"SHLVL";s:1:"0";s:24:"COMPOSER_ALLOW_SUPERUSER";s:1:"1";s:10:"PHP_CFLAGS";s:83:"-fstack-protector-strong -fpic -fpie -O2 -D_LARGEFILE_SOURCE -D_FILE_OFFSET_BITS=64";s:4:"PATH";s:108:"/app:/app/vendor/bin:/root/.composer/vendor/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin";s:11:"PHP_ASC_URL";s:55:"https://www.php.net/distributions/php-8.0.30.tar.xz.asc";s:12:"PHP_CPPFLAGS";s:83:"-fstack-protector-strong -fpic -fpie -O2 -D_LARGEFILE_SOURCE -D_FILE_OFFSET_BITS=64";s:4:"USER";s:8:"www-data";s:11:"HTTP_COOKIE";s:42:"PHPSESSID=f1392907cd41fdc24dd47eb21ec2c6b6";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:20:"HTTP_ACCEPT_ENCODING";s:17:"gzip, deflate, br";s:9:"HTTP_HOST";s:17:"api.skill-hub.loc";s:18:"HTTP_POSTMAN_TOKEN";s:36:"4231e468-9ee8-48ff-ad1c-4f33438dacd9";s:18:"HTTP_CACHE_CONTROL";s:8:"no-cache";s:11:"HTTP_ACCEPT";s:3:"*/*";s:15:"HTTP_USER_AGENT";s:21:"PostmanRuntime/7.44.1";s:15:"REDIRECT_STATUS";s:3:"200";s:11:"SERVER_NAME";s:17:"api.skill-hub.loc";s:11:"SERVER_PORT";s:2:"80";s:11:"SERVER_ADDR";s:10:"**********";s:11:"REMOTE_PORT";s:5:"49032";s:11:"REMOTE_ADDR";s:10:"**********";s:15:"SERVER_SOFTWARE";s:12:"nginx/1.28.0";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:14:"REQUEST_SCHEME";s:4:"http";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:13:"DOCUMENT_ROOT";s:12:"/app/api/web";s:12:"DOCUMENT_URI";s:10:"/index.php";s:11:"REQUEST_URI";s:18:"/common/main/index";s:11:"SCRIPT_NAME";s:10:"/index.php";s:14:"CONTENT_LENGTH";s:0:"";s:12:"CONTENT_TYPE";s:0:"";s:14:"REQUEST_METHOD";s:3:"GET";s:12:"QUERY_STRING";s:0:"";s:15:"SCRIPT_FILENAME";s:22:"/app/api/web/index.php";s:9:"FCGI_ROLE";s:9:"RESPONDER";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:1752494719.666035;s:12:"REQUEST_TIME";i:1752494719;s:4:"argv";a:0:{}s:4:"argc";i:0;s:9:"YII_DEBUG";s:4:"true";s:7:"YII_ENV";s:3:"dev";s:15:"APP_MAINTENANCE";s:1:"0";s:11:"LINK_ASSETS";s:4:"true";s:6:"DB_DSN";s:42:"pgsql:host=db;port=5432;dbname=skillhub_db";s:11:"DB_USERNAME";s:8:"postgres";s:11:"DB_PASSWORD";s:8:"postgres";s:15:"DB_TABLE_PREFIX";s:0:"";s:11:"TEST_DB_DSN";s:38:"pgsql:host=db;port=5432;dbname=test_db";s:16:"TEST_DB_USERNAME";s:8:"postgres";s:16:"TEST_DB_PASSWORD";s:8:"postgres";s:20:"TEST_DB_TABLE_PREFIX";s:0:"";s:13:"API_HOST_INFO";s:37:"http://api.yii2-starter-kit.localhost";s:18:"FRONTEND_HOST_INFO";s:33:"http://yii2-starter-kit.localhost";s:17:"BACKEND_HOST_INFO";s:41:"http://backend.yii2-starter-kit.localhost";s:17:"STORAGE_HOST_INFO";s:41:"http://storage.yii2-starter-kit.localhost";s:9:"SMTP_HOST";s:11:"mailcatcher";s:9:"SMTP_PORT";s:4:"1025";s:30:"FRONTEND_COOKIE_VALIDATION_KEY";s:15:"<generated_key>";s:29:"BACKEND_COOKIE_VALIDATION_KEY";s:15:"<generated_key>";s:11:"ADMIN_EMAIL";s:32:"<EMAIL>";s:11:"ROBOT_EMAIL";s:32:"<EMAIL>";s:16:"GITHUB_CLIENT_ID";s:14:"your-client-id";s:20:"GITHUB_CLIENT_SECRET";s:18:"your-client-secret";s:14:"GLIDE_SIGN_KEY";s:15:"<generated_key>";s:20:"GLIDE_MAX_IMAGE_SIZE";s:7:"4000000";s:29:"COMPOSE_CONVERT_WINDOWS_PATHS";s:1:"1";s:18:"TELEGRAM_BOT_TOKEN";s:46:"**********************************************";s:25:"TELEGRAM_CONTACT_GROUP_ID";s:14:"-1002758503392";}s:3:"GET";a:0:{}s:4:"POST";a:0:{}s:6:"COOKIE";a:1:{s:9:"PHPSESSID";s:32:"f1392907cd41fdc24dd47eb21ec2c6b6";}s:5:"FILES";a:0:{}s:7:"SESSION";a:1:{s:7:"__flash";a:0:{}}}";s:4:"user";s:2:"N;";s:5:"asset";s:6:"a:0:{}";s:7:"summary";a:12:{s:3:"tag";s:13:"6874f28256441";s:3:"url";s:42:"http://api.skill-hub.loc/common/main/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:10:"**********";s:4:"time";d:1752494719.666035;s:10:"statusCode";i:200;s:8:"sqlCount";i:30;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:2409656;s:14:"processingTime";d:2.2841060161590576;}s:10:"exceptions";a:0:{}}