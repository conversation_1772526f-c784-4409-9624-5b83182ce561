<?php

namespace api\modules\common\forms;

use api\modules\common\resources\VacancyResource;
use common\base\BaseModel;
use common\enums\StatusEnum;
use yii\db\StaleObjectException;

class VacancyDeleteForm extends BaseModel
{
    public function __construct(
        public ?VacancyResource $model = null,
        $config = [],
    )
    {
        parent::__construct($config);
    }

    /**
     * @throws StaleObjectException
     * @throws \Throwable
     */
    public function getResult(): bool|int
    {
        $this->model->status = StatusEnum::STATUS_DELETED;
        return $this->model->delete();
    }
}