<?php


namespace api\modules\common\resources;


use common\models\File;
use common\models\JobSpecification;
use common\models\Vacancy;

class MainResource extends JobSpecification
{
    public function fields()
    {
        return [
            'id',
            'name_uz',
            'icon_id' => function($model){
                $model->icon ?? null;
            },
            'vacancy_count' => function($model) {
                return $model->vacancyCount ?? 0;
            },
        ];
    }

    public function getVacancyCount(){
        return $this->hasMany(Vacancy::class , ['category_type_id' => 'id'])->count();
    }

    public function getIcon()
    {
        return $this->hasOne(File::class , ['icon_id' => 'id']);
    }
}