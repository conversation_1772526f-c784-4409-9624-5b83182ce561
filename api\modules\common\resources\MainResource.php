<?php


namespace api\modules\common\resources;


use api\modules\common\resources\FileResource;
use common\models\File;
use common\models\JobSpecification;
use common\models\Vacancy;
class MainResource extends JobSpecification
{
    public function fields()
    {
        return [
            'id',
            'name_uz',
            'vacancy_count' => function($model) {
                return $model->vacancyCount ?? 0;
            },
            'icon' => function($model){
                return $model->icon ?? null;
            },
        ];
    }

    public function getVacancyCount(){
        return $this->hasMany(Vacancy::class , ['category_type_id' => 'id'])->count();
    }

    public function getIcon()
    {
        return $this->hasOne(FileResource::class , ['id' => 'icon_id']);
    }
}