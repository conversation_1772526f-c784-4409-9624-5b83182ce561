<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return [
    'API access token' => 'Token de acesso à API',
    'Email' => 'Email',
    'Expire At' => 'Expira em',
    'Not Active' => 'Inativo',
    'Password' => 'Senha',
    'Roles' => 'Função',
    'Token' => 'Token',
    '"{attribute}" must be a valid JSON' => '"{attribute}" precisa ser um JSON válido',
    'Active' => 'Ativo',
    'Article ID' => 'ID do artigo',
    'Article View' => 'Ver Artigo',
    'Author' => 'Autor',
    'Base URL' => 'URL Base',
    'Base Url' => 'Url Base',
    'Body' => 'Corpo',
    'Caption' => 'Ttulo',
    'Carousel ID' => 'ID do Carrosel',
    'Category' => 'Categoria',
    'Comment' => 'Comentário',
    'Component' => 'Componente',
    'Config' => 'Configuração',
    'Created At' => 'Criado Em',
    'Created at' => 'Criado em',
    'Deleted' => 'Excluído',
    'Down to maintenance.' => 'Desativado para manutenção.',
    'E-mail' => 'E-mail',
    'File Type' => 'Tipo de arquivo',
    'Firstname' => 'Primeiro Nome',
    'Gender' => 'Sexo',
    'ID' => 'ID',
    'Image' => 'Imagem',
    'Key' => 'Chave',
    'Last login' => 'Último login',
    'Lastname' => 'Sobrenome',
    'Locale' => 'Local',
    'Middlename' => 'Segundo nome',
    'Name' => 'Nome',
    'Order' => 'Ordem',
    'Page View' => 'Ver página',
    'Parent Category' => 'Categoria Superior',
    'Path' => 'Caminho',
    'Picture' => 'Imagem',
    'Published' => 'Publicado',
    'Published At' => 'Publicado em',
    'Size' => 'Tamanho',
    'Slug' => 'Slug',
    'Status' => 'Situação',
    'Thumbnail' => 'Miniatura',
    'Title' => 'Título',
    'Type' => 'Tipo',
    'Updated At' => 'Modificado Em',
    'Updated at' => 'Modificado em',
    'Updater' => 'Modificado por',
    'Upload Ip' => 'IP',
    'Url' => 'Url',
    'User ID' => 'ID do Usuario',
    'Username' => 'Usuario',
    'Value' => 'Valor',
];
