<?php

namespace api\modules\common\filters;

use api\modules\common\resources\VacancyResource;
use common\base\BaseModel;
use common\enums\JobSpecificationEnum;
use Yii;

class VacancyFilter extends BaseModel
{
    public ?string $name = null;
    public ?string $category = null;
    public ?string $work_type = null;
    public ?string $published = null;

    public function rules(): array
    {
        return [
            [['name', 'category', 'work_type', 'published'], 'safe'],
            ['name', 'trim'],
            ['name', 'string', 'max' => 255],
            ['category', 'exist', 'targetClass' => JobSpecificationEnum::class, 'targetAttribute' => ['category' => 'id'],'filter' => ['type' => JobSpecificationEnum::SPECIFICATION_CATEGORY_TYPE]],
            ['work_type', 'exist', 'targetClass' => JobSpecificationEnum::class, 'targetAttribute' => ['work_type' => 'id'],'filter' => ['type' => JobSpecificationEnum::SPECIFICATION_WORK_TYPE]],
            ['published', 'integer'],
        ];
    }

    public function getResult(): array
    {
        $query = VacancyResource::find()
                    ->where(['deleted_at' => null])
                    ->andFilterWhere(['ilike', 'name_'.Yii::$app->language, $this->name])
                    ->andFilterWhere(['category_type_id' => $this->category])
                    ->andFilterWhere(['work_type_id' => $this->work_type]);
        if ($this->published !== null) {
            $query->andWhere(['>=', 'published_at', strtotime('-'.$this->published.' hours')]);
        }

        return paginate($query);
    }
}