@fa-font-path: "../../../vendor/fortawesome/font-awesome/webfonts";
@import "../../../vendor/fortawesome/font-awesome/less/fontawesome.less";
@import "../../../vendor/fortawesome/font-awesome/less/solid.less";

body {
  & > .header {
    .logo {
      font-family: inherit;
    }
    .navbar .nav > li > a > .badge{
      position: absolute;
      top: 4px;
      right: 0;
      font-size: 75%;
    }
  }

  .navbar-nav > .notifications-menu > .dropdown-menu > li .menu > li > a > .glyphicon,
  .navbar-nav > .notifications-menu > .dropdown-menu > li .menu > li > a > .fa,
  .navbar-nav > .notifications-menu > .dropdown-menu > li .menu > li > a > .ion {
    background: none;
    color: inherit;
  }

  .user-panel > .info {
    padding-left: 10px;
  }

  /* add sorting icons to gridview sort links */
  a.asc:after,
  a.desc:after {
    .fa-icon;
    .fas;
    margin-left: 5px;
  }
  a.asc:after, .sort-ordinal a.asc:after {
    content: @fa-var-sort-amount-down-alt;
  }
  a.desc:after, .sort-ordinal a.desc:after {
    content: @fa-var-sort-amount-down;
  }
  .sort-numerical a.asc:after {
    content: @fa-var-sort-numeric-down;
  }
  .sort-numerical a.desc:after {
    content: @fa-var-sort-numeric-down-alt;
  }
  .grid-view th {
    white-space: nowrap;
  }
  .hint-block {
    display: block;
    margin-top: 5px;
    color: #999;
  }
  .error-summary {
    color: #a94442;
    background: #fdf7f7;
    border-left: 3px solid #eed3d7;
    padding: 10px 20px;
    margin: 0 0 15px 0;
  }
  .table .bootstrap-datetimepicker-widget {
    z-index: 9999;
  }
}

.no-caret::after {
  display: none;
}

.control-sidebar-content {
  .form-group {
    margin-bottom: 0.3rem;

    label, .custom-control-label {
      font-weight: normal;
    }
  }

  button {
    margin-top: 1rem;
  }
}