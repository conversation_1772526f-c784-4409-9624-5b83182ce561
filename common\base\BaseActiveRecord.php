<?php

namespace common\base;

use yii\behaviors\BlameableBehavior;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

class BaseActiveRecord extends ActiveRecord
{
    public function behaviors(): array
    {
        return [
            [
                'class' => TimestampBehavior::class,
            ],
            [
                'class' => BlameableBehavior::class,
            ]
        ];
    }

    public function delete(): bool|int
    {
        if ($this->hasAttribute('deleted_at')) {
            $this->deleted_at = time();
        }
        if ($this->hasAttribute('deleted_by')) {
            $this->deleted_by = getMyId();
        }
        return $this->save();
    }
}