<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message/extract' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return [
    'Account' => '账号',
    'Application' => '应用程序',
    'Application settings' => '系统设置',
    'Application timeline' => '应用程序时间轴',
    'Are you sure you want to delete this item?' => '你确定要删除这条记录?',
    'Are you sure you want to flush this cache?' => '你确定要清空所以缓存?',
    'Article Categories' => '文章分类',
    'Articles' => '文章',
    'CPU Usage' => 'CPU使用量',
    'Cache' => '缓存',
    'Cache entry has been successfully deleted' => '缓存删除成功',
    'Cache has been successfully flushed' => '缓存全部清除成功',
    'Carousel slide was successfully saved' => 'Carousel slide was successfully saved',
    'Category' => '分类',
    'Content' => '内容',
    'Create' => '创建',
    'Create {modelClass}' => '创建 {modelClass}',
    'DB Type' => 'DB 类型',
    'DB Version' => 'DB 版本',
    'Date' => '日期',
    'Delete' => '删除',
    'Delete a value with the specified key from cache' => '根据key删除指定value',
    'Disabled' => '无效',
    'Edit account' => '编辑帐号',
    'Edit profile' => '编辑个人信息',
    'Email' => 'Email',
    'Enabled' => '有效',
    'Error #{id}' => 'Error #{id}',
    'Event' => '事件',
    'External IP' => '外网 IP',
    'Female' => '女性',
    'File Manager' => '文件管理',
    'File Storage Items' => '文件存储记录',
    'Files in storage' => '在存储系统中的文件',
    'Flush' => '清空',
    'Free Swap' => '空闲Swap',
    'Free memory' => '空闲内存',
    'Hostname' => '主机名',
    'I18n Source Messages' => '国际化源信息',
    'ID' => 'ID',
    'If you leave this field empty, the slug will be generated automatically' => '如果该字段留空的话，系统将自动生成一个slug',
    'Incorrect username or password.' => '用户名或密码错误',
    'Internal IP' => '内网IP',
    'Kernel version' => '内核版本',
    'Key' => '键',
    'Key Storage Items' => '键存储',
    'Key-Value Storage' => '键值存储',
    'Language' => '语言',
    'Level' => '等级',
    'Load average' => '负载',
    'Log Time' => '日志时间',
    'Logout' => '退出',
    'Logs' => '日志',
    'Male' => '男性',
    'Member since {0, date, short}' => '用户 {0, date, short}',
    'Memory' => '内存',
    'Memory Usage' => '内存使用率',
    'Message' => 'Message',
    'More info' => '更多信息',
    'Network' => 'Network',
    'No events found' => 'No events found',
    'Number of cores' => '核数',
    'OS' => 'OS',
    'OS Release' => 'OS Release',
    'Off' => 'Off',
    'On' => 'On',
    'Operating System' => '操作系统',
    'PHP Version' => 'PHP 版本',
    'Pages' => 'Pages',
    'Password' => '密码',
    'Password Confirm' => '密码确认',
    'Port' => '端口',
    'Prefix' => 'Prefix',
    'Processor' => '进程',
    'Processor Architecture' => 'Processor Architecture',
    'Profile' => '资料',
    'Real time' => '实时时间',
    'Remember Me' => '记住我',
    'Reset' => '重置',
    'Save' => '保存',
    'Search' => '搜索',
    'Select cache' => '选择缓存',
    'Settings was successfully saved' => '设置保存成功',
    'Sign In' => '登录',
    'Software' => '软件',
    'Sorry, application failed to collect information about your system. See {link}.' => '对不起，收集系统信息失败。{link}',
    'Static pages' => '静态页面',
    'System' => '系统',
    'System Date' => '系统日期',
    'System Information' => '系统信息',
    'System Logs' => '系统日志',
    'System Time' => '系统时间',
    'Tag' => '标签',
    'TagDependency was invalidated' => '标签分割',
    'Text Blocks' => 'Text Blocks',
    'This username has already been taken.' => '用户名被占用',
    'Time' => '时间',
    'Timeline' => '时间轴',
    'Timezone' => '时区',
    'Total Swap' => 'Total Swap',
    'Total memory' => '总金额',
    'Translation' => '翻译',
    'Update' => '更新',
    'Update {modelClass}: ' => '更新 {modelClass}:',
    'Uptime' => '运行时间',
    'Used size' => '已用大小',
    'User Registrations' => '用户注册数',
    'Username' => '用户名',
    'Users' => '用户',
    'View all' => '查看全部',
    'Web Server' => 'Web 服务',
    'Widget Carousel Items' => 'Widget Carousel Items',
    'Widget Carousels' => 'Widget Carousels',
    'Widget Menus' => 'Widget Menus',
    'You have new event' => '你有新的事件',
    'You have {num} log items' => '你有 {num} 条日志',
    'Your account has been successfully saved' => '您的帐号保存成功',
    'Your profile has been successfully saved' => '您的信息保存成功',
    'Add New {modelClass}' => '',
    'Assignments' => '',
    'Body small text' => '',
    'Brand small text' => '',
    'Cache Components' => '',
    'Cache elements' => '',
    'Carousel' => '',
    'Categories' => '',
    'Child' => '',
    'Child Items' => '',
    'Clear Logs' => '',
    'Collapsed sidebar' => '',
    'Create a new carousel' => '',
    'Create a new category' => '',
    'Create a new key storage item' => '',
    'Create a new menu' => '',
    'Create a new text block' => '',
    'Created At' => '',
    'Data' => '',
    'Description' => '',
    'Disable sidebar hover/focus auto expand' => '',
    'Files' => '',
    'Fixed footer' => '',
    'Fixed navbar' => '',
    'Fixed sidebar' => '',
    'Flush Cache' => '',
    'Footer small text' => '',
    'Go to logs' => '',
    'Go to timeline' => '',
    'Indent sidebar child menu items' => '',
    'Invalidate tag dependency' => '',
    'Item Name' => '',
    'Items' => '',
    'Login' => '',
    'Main' => '',
    'Maintenance mode' => '',
    'Manager' => '',
    'Menu' => '',
    'Mini sidebar' => '',
    'Name' => '',
    'Navbar small text' => '',
    'New user! {identity} has signed up' => '',
    'No navbar border' => '',
    'Oops! Something went wrong... You may audit the error by reviewing the system logs or the application timeline.' => '',
    'Parent' => '',
    'Please select a child item...' => '',
    'Please select a parent item...' => '',
    'Please select a rule...' => '',
    'Please select a type...' => '',
    'Please select an item...' => '',
    'Please select an user...' => '',
    'RBAC Rules' => '',
    'Rule Name' => '',
    'Rules' => '',
    'Save Changes' => '',
    'Sidebar compact style' => '',
    'Sidebar flat style' => '',
    'Sidebar legacy style' => '',
    'Sidebar small text' => '',
    'Sign in to start your session' => '',
    'Storage' => '',
    'The logs have been cleared' => '',
    'This email has already been taken.' => '',
    'Translations' => '',
    'Type' => '',
    'Updated At' => '',
    'Uploads' => '',
    'User' => '',
    'User ID' => '',
    'Widgets' => '',
    '{identity} has been deleted' => '',
];
