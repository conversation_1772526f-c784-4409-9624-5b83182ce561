<?php

namespace api\modules\common\controllers;

use common\enums\FileEnum;
use Yii;
use common\base\BaseController;
use common\enums\StatusEnum;
use api\modules\common\forms\FileForm;
use api\modules\common\resources\FileResource;
use yii\web\NotFoundHttpException;
use yii\web\Response;

class FileController extends BaseController
{

    /**
     * @throws NotFoundHttpException
     */
    public function actionView($id): array
    {
        return $this->sendModel($this->findOne($id));
    }

    /**
     *  Upload document file
     *  @return array
     */
    public function actionCreate(): array
    {
        return $this->sendResponse(
            new FileForm(new FileResource()),
            Yii::$app->request->bodyParams,
        );
    }

    /**
     *  Upload image file
     *  @return array
     */
    public function actionCreateImage(): array
    {
        return $this->sendResponse(
            new FileForm(new FileResource(), FileEnum::FILE_TYPE_IMAGE),
            Yii::$app->request->bodyParams,
        );
    }

    /**
     * @param $id
     * @return Response|null
     * @throws NotFoundHttpException
     */
    public function actionDownload($id): Response|null
    {
        $model = $this->findOne($id);
        $path = Yii::getAlias('@uploads') . '/company/' . $model->day .'/'. $model->path;
        if (!file_exists($path))
            return null;
        return Yii::$app->response->sendFile($path, $model->title);
    }

    /**
     * @throws NotFoundHttpException
     */
    private function findOne($id): FileResource
    {
        $model = FileResource::findOne(['id' => $id,'deleted_at' => null,'status' => StatusEnum::STATUS_ACTIVE]);
        if (!$model)
            throw new  NotFoundHttpException('Модель не найдена.');
        return $model;
    }
}