<?php

namespace api\modules\common\forms;

use api\modules\common\resources\FileResource;
use api\modules\common\resources\JobSpecificationResource;
use common\base\BaseModel;
use common\enums\JobSpecificationEnum;

class JobSpecificationForm extends BaseModel
{
    public ?int $icon_id = null;
    public ?string $name_uz = null;
    public ?string $name_ru = null;
    public ?string $name_en = null;
    public ?string $name_uzk = null;

    public function __construct(
        public ?int $type = null,
        public ?JobSpecificationResource $model = null,
        $config = [],
    )
    {
        parent::__construct($config);
    }
    public function rules(): array
    {
        return [
            [['name_uz', 'name_ru', 'name_en'], 'required'],
            [['name_uzk','icon_id'],'safe'],
            [['name_uz', 'name_ru', 'name_en', 'name_uzk'], 'string', 'max' => 255],
            [['type','icon_id'],'integer'],
            ['type','in', 'range' => JobSpecificationEnum::SPECIFICATION_LIST],
            ['icon_id','exist', 'targetClass' => FileResource::class, 'targetAttribute' => ['icon_id' => 'id'],'filter' => ['deleted_at' => null]],
        ];
    }

    public function getResult(): bool
    {
        if ($this->type == JobSpecificationEnum::SPECIFICATION_CATEGORY_TYPE && $this->icon_id == null) {
            $this->addError('icon_id', 'icon_id is required');
            return false;
        }
        $this->model->attributes = [
            'name_uz' => $this->name_uz,
            'name_ru' => $this->name_ru,
            'name_en' => $this->name_en,
            'name_uzk' => $this->name_uzk,
            'type' => $this->type,
            'icon_id' => $this->icon_id,
        ];

        if (!$this->model->save())
        {
            $this->addErrors($this->model->errors);
            return false;
        }
        return true;
    }
}