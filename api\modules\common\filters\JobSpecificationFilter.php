<?php

namespace api\modules\common\filters;

use api\modules\common\resources\JobSpecificationResource;
use common\base\BaseModel;

class JobSpecificationFilter extends BaseModel
{
    public ?string $title_uz = null;
    public ?string $title_ru = null;
    public ?string $title_en = null;
    public ?string $title_uzk = null;

    public function __construct(
        public ?int $type = null,
        $config = [],
    )
    {
        parent::__construct($config);
    }

    public function getResult(): array
    {
        $query = JobSpecificationResource::find()
            ->where(['type' => $this->type,'deleted_at' => null])
            ->andFilterWhere(['like', 'title_uz', $this->title_uz])
            ->andFilterWhere(['like', 'title_ru', $this->title_ru])
            ->andFilterWhere(['like', 'title_en', $this->title_en])
            ->andFilterWhere(['like', 'title_uzk', $this->title_uzk]);

        return $query->all();
    }
}