a:14:{s:6:"config";s:8708:"a:5:{s:10:"phpVersion";s:6:"8.0.30";s:10:"yiiVersion";s:6:"2.0.46";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.46";s:4:"name";s:16:"Yii2 Starter Kit";s:7:"version";s:3:"1.0";s:8:"language";s:5:"ru-RU";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";s:4:"true";}s:3:"php";a:5:{s:7:"version";s:6:"8.0.30";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:37:{s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:7:"2.0.7.0";s:5:"alias";a:1:{s:8:"@yii/jui";s:28:"/app/vendor/yiisoft/yii2-jui";}}s:22:"alexantr/yii2-elfinder";a:3:{s:4:"name";s:22:"alexantr/yii2-elfinder";s:7:"version";s:7:"1.8.0.0";s:5:"alias";a:1:{s:18:"@alexantr/elfinder";s:38:"/app/vendor/alexantr/yii2-elfinder/src";}}s:30:"asofter/yii2-imperavi-redactor";a:3:{s:4:"name";s:30:"asofter/yii2-imperavi-redactor";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:13:"@yii/imperavi";s:55:"/app/vendor/asofter/yii2-imperavi-redactor/yii/imperavi";}}s:30:"kartik-v/yii2-widget-typeahead";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-typeahead";s:7:"version";s:7:"1.0.4.0";s:5:"alias";a:1:{s:17:"@kartik/typeahead";s:46:"/app/vendor/kartik-v/yii2-widget-typeahead/src";}}s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:7:"3.0.5.0";s:5:"alias";a:1:{s:12:"@kartik/base";s:41:"/app/vendor/kartik-v/yii2-krajee-base/src";}}s:30:"kartik-v/yii2-widget-touchspin";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-touchspin";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:17:"@kartik/touchspin";s:46:"/app/vendor/kartik-v/yii2-widget-touchspin/src";}}s:31:"kartik-v/yii2-widget-timepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-timepicker";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:12:"@kartik/time";s:47:"/app/vendor/kartik-v/yii2-widget-timepicker/src";}}s:32:"kartik-v/yii2-widget-switchinput";a:3:{s:4:"name";s:32:"kartik-v/yii2-widget-switchinput";s:7:"version";s:7:"1.3.1.0";s:5:"alias";a:1:{s:19:"@kartik/switchinput";s:44:"/app/vendor/kartik-v/yii2-widget-switchinput";}}s:28:"kartik-v/yii2-widget-spinner";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-spinner";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/spinner";s:44:"/app/vendor/kartik-v/yii2-widget-spinner/src";}}s:28:"kartik-v/yii2-widget-sidenav";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-sidenav";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/sidenav";s:40:"/app/vendor/kartik-v/yii2-widget-sidenav";}}s:27:"kartik-v/yii2-widget-rating";a:3:{s:4:"name";s:27:"kartik-v/yii2-widget-rating";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:14:"@kartik/rating";s:43:"/app/vendor/kartik-v/yii2-widget-rating/src";}}s:31:"kartik-v/yii2-widget-rangeinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-rangeinput";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:13:"@kartik/range";s:47:"/app/vendor/kartik-v/yii2-widget-rangeinput/src";}}s:26:"kartik-v/yii2-widget-growl";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-growl";s:7:"version";s:7:"1.1.2.0";s:5:"alias";a:1:{s:13:"@kartik/growl";s:42:"/app/vendor/kartik-v/yii2-widget-growl/src";}}s:28:"kartik-v/yii2-widget-select2";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-select2";s:7:"version";s:7:"2.2.4.0";s:5:"alias";a:1:{s:15:"@kartik/select2";s:44:"/app/vendor/kartik-v/yii2-widget-select2/src";}}s:28:"kartik-v/yii2-widget-depdrop";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-depdrop";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:15:"@kartik/depdrop";s:44:"/app/vendor/kartik-v/yii2-widget-depdrop/src";}}s:30:"kartik-v/yii2-widget-fileinput";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-fileinput";s:7:"version";s:7:"1.1.1.0";s:5:"alias";a:1:{s:12:"@kartik/file";s:46:"/app/vendor/kartik-v/yii2-widget-fileinput/src";}}s:26:"kartik-v/yii2-widget-alert";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-alert";s:7:"version";s:7:"1.1.5.0";s:5:"alias";a:1:{s:13:"@kartik/alert";s:42:"/app/vendor/kartik-v/yii2-widget-alert/src";}}s:31:"kartik-v/yii2-widget-colorinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-colorinput";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:13:"@kartik/color";s:47:"/app/vendor/kartik-v/yii2-widget-colorinput/src";}}s:26:"kartik-v/yii2-widget-affix";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-affix";s:7:"version";s:7:"1.0.0.0";s:5:"alias";a:1:{s:13:"@kartik/affix";s:38:"/app/vendor/kartik-v/yii2-widget-affix";}}s:31:"kartik-v/yii2-widget-activeform";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-activeform";s:7:"version";s:7:"1.6.2.0";s:5:"alias";a:1:{s:12:"@kartik/form";s:47:"/app/vendor/kartik-v/yii2-widget-activeform/src";}}s:21:"kartik-v/yii2-widgets";a:3:{s:4:"name";s:21:"kartik-v/yii2-widgets";s:7:"version";s:7:"3.4.1.0";s:5:"alias";a:1:{s:15:"@kartik/widgets";s:37:"/app/vendor/kartik-v/yii2-widgets/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:7:"1.5.1.0";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:51:"/app/vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:31:"kartik-v/yii2-widget-datepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-datepicker";s:7:"version";s:7:"1.4.8.0";s:5:"alias";a:1:{s:12:"@kartik/date";s:47:"/app/vendor/kartik-v/yii2-widget-datepicker/src";}}s:20:"trntv/yii2-aceeditor";a:3:{s:4:"name";s:20:"trntv/yii2-aceeditor";s:7:"version";s:7:"2.1.2.0";s:5:"alias";a:1:{s:16:"@trntv/aceeditor";s:36:"/app/vendor/trntv/yii2-aceeditor/src";}}s:22:"trntv/yii2-command-bus";a:3:{s:4:"name";s:22:"trntv/yii2-command-bus";s:7:"version";s:7:"3.2.0.0";s:5:"alias";a:1:{s:10:"@trntv/bus";s:38:"/app/vendor/trntv/yii2-command-bus/src";}}s:18:"yiisoft/yii2-queue";a:3:{s:4:"name";s:18:"yiisoft/yii2-queue";s:7:"version";s:7:"2.3.4.0";s:5:"alias";a:11:{s:10:"@yii/queue";s:34:"/app/vendor/yiisoft/yii2-queue/src";s:13:"@yii/queue/db";s:45:"/app/vendor/yiisoft/yii2-queue/src/drivers/db";s:14:"@yii/queue/sqs";s:46:"/app/vendor/yiisoft/yii2-queue/src/drivers/sqs";s:15:"@yii/queue/amqp";s:47:"/app/vendor/yiisoft/yii2-queue/src/drivers/amqp";s:15:"@yii/queue/file";s:47:"/app/vendor/yiisoft/yii2-queue/src/drivers/file";s:15:"@yii/queue/sync";s:47:"/app/vendor/yiisoft/yii2-queue/src/drivers/sync";s:16:"@yii/queue/redis";s:48:"/app/vendor/yiisoft/yii2-queue/src/drivers/redis";s:16:"@yii/queue/stomp";s:48:"/app/vendor/yiisoft/yii2-queue/src/drivers/stomp";s:18:"@yii/queue/gearman";s:50:"/app/vendor/yiisoft/yii2-queue/src/drivers/gearman";s:20:"@yii/queue/beanstalk";s:52:"/app/vendor/yiisoft/yii2-queue/src/drivers/beanstalk";s:23:"@yii/queue/amqp_interop";s:55:"/app/vendor/yiisoft/yii2-queue/src/drivers/amqp_interop";}}s:26:"trntv/yii2-datetime-widget";a:3:{s:4:"name";s:26:"trntv/yii2-datetime-widget";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:19:"@trntv/yii/datetime";s:42:"/app/vendor/trntv/yii2-datetime-widget/src";}}s:16:"trntv/yii2-glide";a:3:{s:4:"name";s:16:"trntv/yii2-glide";s:7:"version";s:7:"1.2.2.0";s:5:"alias";a:1:{s:12:"@trntv/glide";s:32:"/app/vendor/trntv/yii2-glide/src";}}s:24:"rmrevin/yii2-fontawesome";a:3:{s:4:"name";s:24:"rmrevin/yii2-fontawesome";s:7:"version";s:7:"3.7.0.0";s:5:"alias";a:1:{s:24:"@rmrevin/yii/fontawesome";s:36:"/app/vendor/rmrevin/yii2-fontawesome";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"2.0.14.0";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:39:"/app/vendor/yiisoft/yii2-httpclient/src";}}s:30:"yii2-starter-kit/yii2-file-kit";a:3:{s:4:"name";s:30:"yii2-starter-kit/yii2-file-kit";s:7:"version";s:7:"2.1.5.0";s:5:"alias";a:1:{s:14:"@trntv/filekit";s:46:"/app/vendor/yii2-starter-kit/yii2-file-kit/src";}}s:23:"yiisoft/yii2-authclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-authclient";s:7:"version";s:8:"2.2.13.0";s:5:"alias";a:1:{s:15:"@yii/authclient";s:39:"/app/vendor/yiisoft/yii2-authclient/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.10.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:39:"/app/vendor/yiisoft/yii2-bootstrap4/src";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/faker";s:34:"/app/vendor/yiisoft/yii2-faker/src";}}s:24:"yiisoft/yii2-swiftmailer";a:3:{s:4:"name";s:24:"yiisoft/yii2-swiftmailer";s:7:"version";s:7:"2.1.3.0";s:5:"alias";a:1:{s:16:"@yii/swiftmailer";s:40:"/app/vendor/yiisoft/yii2-swiftmailer/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.5.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:32:"/app/vendor/yiisoft/yii2-gii/src";}}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.21.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:34:"/app/vendor/yiisoft/yii2-debug/src";}}}}";s:3:"log";s:41477:"a:1:{s:8:"messages";a:59:{i:0;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752498188.749606;i:4;a:0:{}i:5;i:1136232;}i:1;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1752498188.749923;i:4;a:0:{}i:5;i:1137408;}i:2;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752498188.770118;i:4;a:0:{}i:5;i:1142968;}i:3;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1752498188.839883;i:4;a:0:{}i:5;i:1241096;}i:4;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752498189.168391;i:4;a:0:{}i:5;i:1425240;}i:5;a:6:{i:0;s:69:"Bootstrap with common\components\maintenance\Maintenance::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752498189.19241;i:4;a:0:{}i:5;i:1439336;}i:6;a:6:{i:0;s:65:"Opening DB connection: pgsql:host=db;port=5432;dbname=skillhub_db";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1752498189.508047;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:71;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:113;s:8:"function";s:8:"getModel";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:28:"/app/frontend/config/web.php";s:4:"line";i:46;s:8:"function";s:3:"get";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}}i:5;i:1785040;}i:9;a:6:{i:0;s:67:"SELECT * FROM "key_storage_item" WHERE "key"='frontend.maintenance'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752498189.601293;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:71;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:113;s:8:"function";s:8:"getModel";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:28:"/app/frontend/config/web.php";s:4:"line";i:46;s:8:"function";s:3:"get";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}}i:5;i:1789064;}i:12;a:6:{i:0;s:2822:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'key_storage_item'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752498189.719952;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:71;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:113;s:8:"function";s:8:"getModel";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:28:"/app/frontend/config/web.php";s:4:"line";i:46;s:8:"function";s:3:"get";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}}i:5;i:1812048;}i:15;a:6:{i:0;s:884:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='key_storage_item'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752498189.827311;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:71;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:113;s:8:"function";s:8:"getModel";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:28:"/app/frontend/config/web.php";s:4:"line";i:46;s:8:"function";s:3:"get";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}}i:5;i:1827696;}i:18;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:1752498189.8396;i:4;a:1:{i:0;a:5:{s:4:"file";s:40:"/app/common/behaviors/LocaleBehavior.php";s:4:"line";i:41;s:8:"function";s:8:"hasFlash";s:5:"class";s:15:"yii\web\Session";s:4:"type";s:2:"->";}}i:5;i:1825296;}i:32;a:6:{i:0;s:19:"Route requested: ''";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:1752498189.868272;i:4;a:0:{}i:5;i:1915760;}i:33;a:6:{i:0;s:24:"Route to run: site/index";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:1752498189.905905;i:4;a:0:{}i:5;i:1934448;}i:34;a:6:{i:0;s:66:"Running action: frontend\controllers\SiteController::actionIndex()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:1752498190.090169;i:4;a:0:{}i:5;i:1948536;}i:35;a:6:{i:0;s:55:"Rendering view file: /app/frontend/views/site/index.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752498190.117995;i:4;a:1:{i:0;a:5:{s:4:"file";s:44:"/app/frontend/controllers/SiteController.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:1957824;}i:36;a:6:{i:0;s:284:"SELECT "widget_carousel_item".* FROM "widget_carousel_item" LEFT JOIN "widget_carousel" ON "widget_carousel_item"."carousel_id" = "widget_carousel"."id" WHERE ("widget_carousel_item"."status"=1) AND ("widget_carousel"."status"=1) AND ("widget_carousel"."key"='index') ORDER BY "order"";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752498190.413288;i:4;a:3:{i:0;a:5:{s:4:"file";s:34:"/app/common/widgets/DbCarousel.php";s:4:"line";i:65;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:34:"/app/frontend/views/site/index.php";s:4:"line";i:10;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"/app/frontend/controllers/SiteController.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:2143624;}i:39;a:6:{i:0;s:2826:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'widget_carousel_item'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752498190.419659;i:4;a:3:{i:0;a:5:{s:4:"file";s:34:"/app/common/widgets/DbCarousel.php";s:4:"line";i:65;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:34:"/app/frontend/views/site/index.php";s:4:"line";i:10;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"/app/frontend/controllers/SiteController.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:2172408;}i:42;a:6:{i:0;s:888:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='widget_carousel_item'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752498190.435862;i:4;a:3:{i:0;a:5:{s:4:"file";s:34:"/app/common/widgets/DbCarousel.php";s:4:"line";i:65;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:34:"/app/frontend/views/site/index.php";s:4:"line";i:10;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"/app/frontend/controllers/SiteController.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:2185680;}i:45;a:6:{i:0;s:44:"SELECT * FROM "widget_carousel" WHERE "id"=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752498190.463339;i:4;a:3:{i:0;a:5:{s:4:"file";s:34:"/app/common/widgets/DbCarousel.php";s:4:"line";i:65;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:34:"/app/frontend/views/site/index.php";s:4:"line";i:10;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"/app/frontend/controllers/SiteController.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:2197320;}i:48;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'widget_carousel'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752498190.464899;i:4;a:3:{i:0;a:5:{s:4:"file";s:34:"/app/common/widgets/DbCarousel.php";s:4:"line";i:65;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:34:"/app/frontend/views/site/index.php";s:4:"line";i:10;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"/app/frontend/controllers/SiteController.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:2215904;}i:51;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='widget_carousel'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752498190.475864;i:4;a:3:{i:0;a:5:{s:4:"file";s:34:"/app/common/widgets/DbCarousel.php";s:4:"line";i:65;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:34:"/app/frontend/views/site/index.php";s:4:"line";i:10;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"/app/frontend/controllers/SiteController.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:2224744;}i:54;a:6:{i:0;s:6920:"ReflectionException: Class "fileStorage" does not exist in /app/vendor/yiisoft/yii2/di/Container.php:507
Stack trace:
#0 /app/vendor/yiisoft/yii2/di/Container.php(507): ReflectionClass->__construct('fileStorage')
#1 /app/vendor/yiisoft/yii2/di/Container.php(385): yii\di\Container->getDependencies('fileStorage')
#2 /app/vendor/yiisoft/yii2/di/Container.php(170): yii\di\Container->build('fileStorage', Array, Array)
#3 /app/vendor/yiisoft/yii2/di/Instance.php(175): yii\di\Container->get('fileStorage')
#4 /app/vendor/yiisoft/yii2/di/Instance.php(144): yii\di\Instance->get(NULL)
#5 /app/vendor/yii2-starter-kit/yii2-file-kit/src/behaviors/UploadBehavior.php(328): yii\di\Instance::ensure(Object(yii\di\Instance), 'trntv\\filekit\\S...')
#6 /app/vendor/yii2-starter-kit/yii2-file-kit/src/behaviors/UploadBehavior.php(396): trntv\filekit\behaviors\UploadBehavior->getStorage()
#7 /app/vendor/yii2-starter-kit/yii2-file-kit/src/behaviors/UploadBehavior.php(273): trntv\filekit\behaviors\UploadBehavior->enrichFileData(Array)
#8 [internal function]: trntv\filekit\behaviors\UploadBehavior->afterFindSingle(Object(yii\base\Event))
#9 /app/vendor/yiisoft/yii2/base/Component.php(633): call_user_func(Array, Object(yii\base\Event))
#10 /app/vendor/yiisoft/yii2/db/BaseActiveRecord.php(942): yii\base\Component->trigger('afterFind')
#11 /app/vendor/yiisoft/yii2/db/ActiveQuery.php(233): yii\db\BaseActiveRecord->afterFind()
#12 /app/vendor/yiisoft/yii2/db/Query.php(251): yii\db\ActiveQuery->populate(Array)
#13 /app/vendor/yiisoft/yii2/db/ActiveQuery.php(133): yii\db\Query->all(NULL)
#14 /app/common/widgets/DbCarousel.php(65): yii\db\ActiveQuery->all()
#15 /app/vendor/yiisoft/yii2/base/BaseObject.php(109): common\widgets\DbCarousel->init()
#16 [internal function]: yii\base\BaseObject->__construct(Array)
#17 /app/vendor/yiisoft/yii2/di/Container.php(419): ReflectionClass->newInstanceArgs(Array)
#18 /app/vendor/yiisoft/yii2/di/Container.php(170): yii\di\Container->build('common\\widgets\\...', Array, Array)
#19 /app/vendor/yiisoft/yii2/BaseYii.php(365): yii\di\Container->get('common\\widgets\\...', Array, Array)
#20 /app/vendor/yiisoft/yii2/base/Widget.php(143): yii\BaseYii::createObject(Array)
#21 /app/frontend/views/site/index.php(10): yii\base\Widget::widget(Array)
#22 /app/vendor/yiisoft/yii2/base/View.php(347): require('/app/frontend/v...')
#23 /app/vendor/yiisoft/yii2/base/View.php(257): yii\base\View->renderPhpFile('/app/frontend/v...', Array)
#24 /app/vendor/yiisoft/yii2/base/View.php(156): yii\base\View->renderFile('/app/frontend/v...', Array, Object(frontend\controllers\SiteController))
#25 /app/vendor/yiisoft/yii2/base/Controller.php(407): yii\base\View->render('index', Array, Object(frontend\controllers\SiteController))
#26 /app/frontend/controllers/SiteController.php(60): yii\base\Controller->render('index')
#27 [internal function]: frontend\controllers\SiteController->actionIndex()
#28 /app/vendor/yiisoft/yii2/base/InlineAction.php(57): call_user_func_array(Array, Array)
#29 /app/vendor/yiisoft/yii2/base/Controller.php(178): yii\base\InlineAction->runWithParams(Array)
#30 /app/vendor/yiisoft/yii2/base/Module.php(552): yii\base\Controller->runAction('index', Array)
#31 /app/vendor/yiisoft/yii2/web/Application.php(103): yii\base\Module->runAction('', Array)
#32 /app/vendor/yiisoft/yii2/base/Application.php(384): yii\web\Application->handleRequest(Object(yii\web\Request))
#33 /app/frontend/web/index.php(22): yii\base\Application->run()
#34 {main}

Next yii\di\NotInstantiableException: Failed to instantiate component or class "fileStorage". in /app/vendor/yiisoft/yii2/di/Container.php:509
Stack trace:
#0 /app/vendor/yiisoft/yii2/di/Container.php(385): yii\di\Container->getDependencies('fileStorage')
#1 /app/vendor/yiisoft/yii2/di/Container.php(170): yii\di\Container->build('fileStorage', Array, Array)
#2 /app/vendor/yiisoft/yii2/di/Instance.php(175): yii\di\Container->get('fileStorage')
#3 /app/vendor/yiisoft/yii2/di/Instance.php(144): yii\di\Instance->get(NULL)
#4 /app/vendor/yii2-starter-kit/yii2-file-kit/src/behaviors/UploadBehavior.php(328): yii\di\Instance::ensure(Object(yii\di\Instance), 'trntv\\filekit\\S...')
#5 /app/vendor/yii2-starter-kit/yii2-file-kit/src/behaviors/UploadBehavior.php(396): trntv\filekit\behaviors\UploadBehavior->getStorage()
#6 /app/vendor/yii2-starter-kit/yii2-file-kit/src/behaviors/UploadBehavior.php(273): trntv\filekit\behaviors\UploadBehavior->enrichFileData(Array)
#7 [internal function]: trntv\filekit\behaviors\UploadBehavior->afterFindSingle(Object(yii\base\Event))
#8 /app/vendor/yiisoft/yii2/base/Component.php(633): call_user_func(Array, Object(yii\base\Event))
#9 /app/vendor/yiisoft/yii2/db/BaseActiveRecord.php(942): yii\base\Component->trigger('afterFind')
#10 /app/vendor/yiisoft/yii2/db/ActiveQuery.php(233): yii\db\BaseActiveRecord->afterFind()
#11 /app/vendor/yiisoft/yii2/db/Query.php(251): yii\db\ActiveQuery->populate(Array)
#12 /app/vendor/yiisoft/yii2/db/ActiveQuery.php(133): yii\db\Query->all(NULL)
#13 /app/common/widgets/DbCarousel.php(65): yii\db\ActiveQuery->all()
#14 /app/vendor/yiisoft/yii2/base/BaseObject.php(109): common\widgets\DbCarousel->init()
#15 [internal function]: yii\base\BaseObject->__construct(Array)
#16 /app/vendor/yiisoft/yii2/di/Container.php(419): ReflectionClass->newInstanceArgs(Array)
#17 /app/vendor/yiisoft/yii2/di/Container.php(170): yii\di\Container->build('common\\widgets\\...', Array, Array)
#18 /app/vendor/yiisoft/yii2/BaseYii.php(365): yii\di\Container->get('common\\widgets\\...', Array, Array)
#19 /app/vendor/yiisoft/yii2/base/Widget.php(143): yii\BaseYii::createObject(Array)
#20 /app/frontend/views/site/index.php(10): yii\base\Widget::widget(Array)
#21 /app/vendor/yiisoft/yii2/base/View.php(347): require('/app/frontend/v...')
#22 /app/vendor/yiisoft/yii2/base/View.php(257): yii\base\View->renderPhpFile('/app/frontend/v...', Array)
#23 /app/vendor/yiisoft/yii2/base/View.php(156): yii\base\View->renderFile('/app/frontend/v...', Array, Object(frontend\controllers\SiteController))
#24 /app/vendor/yiisoft/yii2/base/Controller.php(407): yii\base\View->render('index', Array, Object(frontend\controllers\SiteController))
#25 /app/frontend/controllers/SiteController.php(60): yii\base\Controller->render('index')
#26 [internal function]: frontend\controllers\SiteController->actionIndex()
#27 /app/vendor/yiisoft/yii2/base/InlineAction.php(57): call_user_func_array(Array, Array)
#28 /app/vendor/yiisoft/yii2/base/Controller.php(178): yii\base\InlineAction->runWithParams(Array)
#29 /app/vendor/yiisoft/yii2/base/Module.php(552): yii\base\Controller->runAction('index', Array)
#30 /app/vendor/yiisoft/yii2/web/Application.php(103): yii\base\Module->runAction('', Array)
#31 /app/vendor/yiisoft/yii2/base/Application.php(384): yii\web\Application->handleRequest(Object(yii\web\Request))
#32 /app/frontend/web/index.php(22): yii\base\Application->run()
#33 {main}";i:1;i:1;i:2;s:31:"yii\di\NotInstantiableException";i:3;d:1752498190.667824;i:4;a:0:{}i:5;i:1965152;}i:55;a:6:{i:0;s:78:"Rendering view file: /app/vendor/yiisoft/yii2/views/errorHandler/exception.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752498190.680958;i:4;a:0:{}i:5;i:1967616;}i:56;a:6:{i:0;s:86:"Rendering view file: /app/vendor/yiisoft/yii2/views/errorHandler/previousException.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752498190.991298;i:4;a:0:{}i:5;i:2031592;}i:57;a:6:{i:0;s:82:"Rendering view file: /app/vendor/yiisoft/yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752498191.087094;i:4;a:0:{}i:5;i:2128856;}i:58;a:6:{i:0;s:82:"Rendering view file: /app/vendor/yiisoft/yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752498191.221832;i:4;a:0:{}i:5;i:2133072;}i:59;a:6:{i:0;s:82:"Rendering view file: /app/vendor/yiisoft/yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752498191.237225;i:4;a:0:{}i:5;i:2139944;}i:60;a:6:{i:0;s:82:"Rendering view file: /app/vendor/yiisoft/yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752498191.250918;i:4;a:0:{}i:5;i:2072048;}i:61;a:6:{i:0;s:82:"Rendering view file: /app/vendor/yiisoft/yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752498191.262751;i:4;a:0:{}i:5;i:2077384;}i:62;a:6:{i:0;s:82:"Rendering view file: /app/vendor/yiisoft/yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752498191.277798;i:4;a:0:{}i:5;i:2096848;}i:63;a:6:{i:0;s:82:"Rendering view file: /app/vendor/yiisoft/yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752498191.29172;i:4;a:0:{}i:5;i:2102184;}i:64;a:6:{i:0;s:82:"Rendering view file: /app/vendor/yiisoft/yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752498191.305023;i:4;a:0:{}i:5;i:2104704;}i:65;a:6:{i:0;s:82:"Rendering view file: /app/vendor/yiisoft/yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752498191.312274;i:4;a:0:{}i:5;i:2071368;}i:66;a:6:{i:0;s:82:"Rendering view file: /app/vendor/yiisoft/yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752498191.329681;i:4;a:0:{}i:5;i:2161152;}i:67;a:6:{i:0;s:82:"Rendering view file: /app/vendor/yiisoft/yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752498191.379801;i:4;a:0:{}i:5;i:2273352;}i:68;a:6:{i:0;s:82:"Rendering view file: /app/vendor/yiisoft/yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752498191.437011;i:4;a:0:{}i:5;i:2174792;}i:69;a:6:{i:0;s:82:"Rendering view file: /app/vendor/yiisoft/yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752498191.478683;i:4;a:0:{}i:5;i:2250376;}i:70;a:6:{i:0;s:82:"Rendering view file: /app/vendor/yiisoft/yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752498191.493996;i:4;a:0:{}i:5;i:2181368;}i:71;a:6:{i:0;s:82:"Rendering view file: /app/vendor/yiisoft/yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752498191.512053;i:4;a:0:{}i:5;i:2106128;}i:72;a:6:{i:0;s:82:"Rendering view file: /app/vendor/yiisoft/yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752498191.528514;i:4;a:0:{}i:5;i:2132648;}i:73;a:6:{i:0;s:82:"Rendering view file: /app/vendor/yiisoft/yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752498191.535279;i:4;a:0:{}i:5;i:2097672;}i:74;a:6:{i:0;s:82:"Rendering view file: /app/vendor/yiisoft/yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752498191.550492;i:4;a:0:{}i:5;i:2194128;}i:75;a:6:{i:0;s:82:"Rendering view file: /app/vendor/yiisoft/yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752498191.563218;i:4;a:0:{}i:5;i:2199464;}i:76;a:6:{i:0;s:82:"Rendering view file: /app/vendor/yiisoft/yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752498191.580589;i:4;a:0:{}i:5;i:2182856;}i:77;a:6:{i:0;s:82:"Rendering view file: /app/vendor/yiisoft/yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752498191.590978;i:4;a:0:{}i:5;i:2152984;}i:78;a:6:{i:0;s:82:"Rendering view file: /app/vendor/yiisoft/yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752498191.602101;i:4;a:0:{}i:5;i:2129208;}i:79;a:6:{i:0;s:82:"Rendering view file: /app/vendor/yiisoft/yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752498191.623274;i:4;a:0:{}i:5;i:2194936;}i:80;a:6:{i:0;s:82:"Rendering view file: /app/vendor/yiisoft/yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752498191.642514;i:4;a:0:{}i:5;i:2205904;}i:81;a:6:{i:0;s:82:"Rendering view file: /app/vendor/yiisoft/yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752498191.659549;i:4;a:0:{}i:5;i:2207144;}i:82;a:6:{i:0;s:82:"Rendering view file: /app/vendor/yiisoft/yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752498191.678552;i:4;a:0:{}i:5;i:2212656;}i:83;a:6:{i:0;s:82:"Rendering view file: /app/vendor/yiisoft/yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752498191.691323;i:4;a:0:{}i:5;i:2151568;}i:84;a:6:{i:0;s:82:"Rendering view file: /app/vendor/yiisoft/yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752498191.698824;i:4;a:0:{}i:5;i:2141520;}i:85;a:6:{i:0;s:82:"Rendering view file: /app/vendor/yiisoft/yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752498191.719201;i:4;a:0:{}i:5;i:2149152;}i:86;a:6:{i:0;s:82:"Rendering view file: /app/vendor/yiisoft/yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752498191.750043;i:4;a:0:{}i:5;i:2225808;}i:87;a:6:{i:0;s:82:"Rendering view file: /app/vendor/yiisoft/yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752498191.783299;i:4;a:0:{}i:5;i:2239568;}i:88;a:6:{i:0;s:82:"Rendering view file: /app/vendor/yiisoft/yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752498191.828764;i:4;a:0:{}i:5;i:2178928;}i:89;a:6:{i:0;s:82:"Rendering view file: /app/vendor/yiisoft/yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752498191.842345;i:4;a:0:{}i:5;i:2237832;}i:90;a:6:{i:0;s:82:"Rendering view file: /app/vendor/yiisoft/yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752498191.848279;i:4;a:0:{}i:5;i:2163936;}i:91;a:6:{i:0;s:7157:"INSERT INTO "system_log" ("level", "category", "log_time", "prefix", "message")
                VALUES (1, 'yii\di\NotInstantiableException', 1752498190.6678, '[frontend][/]', 'ReflectionException: Class "fileStorage" does not exist in /app/vendor/yiisoft/yii2/di/Container.php:507
Stack trace:
#0 /app/vendor/yiisoft/yii2/di/Container.php(507): ReflectionClass->__construct(''fileStorage'')
#1 /app/vendor/yiisoft/yii2/di/Container.php(385): yii\di\Container->getDependencies(''fileStorage'')
#2 /app/vendor/yiisoft/yii2/di/Container.php(170): yii\di\Container->build(''fileStorage'', Array, Array)
#3 /app/vendor/yiisoft/yii2/di/Instance.php(175): yii\di\Container->get(''fileStorage'')
#4 /app/vendor/yiisoft/yii2/di/Instance.php(144): yii\di\Instance->get(NULL)
#5 /app/vendor/yii2-starter-kit/yii2-file-kit/src/behaviors/UploadBehavior.php(328): yii\di\Instance::ensure(Object(yii\di\Instance), ''trntv\\filekit\\S...'')
#6 /app/vendor/yii2-starter-kit/yii2-file-kit/src/behaviors/UploadBehavior.php(396): trntv\filekit\behaviors\UploadBehavior->getStorage()
#7 /app/vendor/yii2-starter-kit/yii2-file-kit/src/behaviors/UploadBehavior.php(273): trntv\filekit\behaviors\UploadBehavior->enrichFileData(Array)
#8 [internal function]: trntv\filekit\behaviors\UploadBehavior->afterFindSingle(Object(yii\base\Event))
#9 /app/vendor/yiisoft/yii2/base/Component.php(633): call_user_func(Array, Object(yii\base\Event))
#10 /app/vendor/yiisoft/yii2/db/BaseActiveRecord.php(942): yii\base\Component->trigger(''afterFind'')
#11 /app/vendor/yiisoft/yii2/db/ActiveQuery.php(233): yii\db\BaseActiveRecord->afterFind()
#12 /app/vendor/yiisoft/yii2/db/Query.php(251): yii\db\ActiveQuery->populate(Array)
#13 /app/vendor/yiisoft/yii2/db/ActiveQuery.php(133): yii\db\Query->all(NULL)
#14 /app/common/widgets/DbCarousel.php(65): yii\db\ActiveQuery->all()
#15 /app/vendor/yiisoft/yii2/base/BaseObject.php(109): common\widgets\DbCarousel->init()
#16 [internal function]: yii\base\BaseObject->__construct(Array)
#17 /app/vendor/yiisoft/yii2/di/Container.php(419): ReflectionClass->newInstanceArgs(Array)
#18 /app/vendor/yiisoft/yii2/di/Container.php(170): yii\di\Container->build(''common\\widgets\\...'', Array, Array)
#19 /app/vendor/yiisoft/yii2/BaseYii.php(365): yii\di\Container->get(''common\\widgets\\...'', Array, Array)
#20 /app/vendor/yiisoft/yii2/base/Widget.php(143): yii\BaseYii::createObject(Array)
#21 /app/frontend/views/site/index.php(10): yii\base\Widget::widget(Array)
#22 /app/vendor/yiisoft/yii2/base/View.php(347): require(''/app/frontend/v...'')
#23 /app/vendor/yiisoft/yii2/base/View.php(257): yii\base\View->renderPhpFile(''/app/frontend/v...'', Array)
#24 /app/vendor/yiisoft/yii2/base/View.php(156): yii\base\View->renderFile(''/app/frontend/v...'', Array, Object(frontend\controllers\SiteController))
#25 /app/vendor/yiisoft/yii2/base/Controller.php(407): yii\base\View->render(''index'', Array, Object(frontend\controllers\SiteController))
#26 /app/frontend/controllers/SiteController.php(60): yii\base\Controller->render(''index'')
#27 [internal function]: frontend\controllers\SiteController->actionIndex()
#28 /app/vendor/yiisoft/yii2/base/InlineAction.php(57): call_user_func_array(Array, Array)
#29 /app/vendor/yiisoft/yii2/base/Controller.php(178): yii\base\InlineAction->runWithParams(Array)
#30 /app/vendor/yiisoft/yii2/base/Module.php(552): yii\base\Controller->runAction(''index'', Array)
#31 /app/vendor/yiisoft/yii2/web/Application.php(103): yii\base\Module->runAction('''', Array)
#32 /app/vendor/yiisoft/yii2/base/Application.php(384): yii\web\Application->handleRequest(Object(yii\web\Request))
#33 /app/frontend/web/index.php(22): yii\base\Application->run()
#34 {main}

Next yii\di\NotInstantiableException: Failed to instantiate component or class "fileStorage". in /app/vendor/yiisoft/yii2/di/Container.php:509
Stack trace:
#0 /app/vendor/yiisoft/yii2/di/Container.php(385): yii\di\Container->getDependencies(''fileStorage'')
#1 /app/vendor/yiisoft/yii2/di/Container.php(170): yii\di\Container->build(''fileStorage'', Array, Array)
#2 /app/vendor/yiisoft/yii2/di/Instance.php(175): yii\di\Container->get(''fileStorage'')
#3 /app/vendor/yiisoft/yii2/di/Instance.php(144): yii\di\Instance->get(NULL)
#4 /app/vendor/yii2-starter-kit/yii2-file-kit/src/behaviors/UploadBehavior.php(328): yii\di\Instance::ensure(Object(yii\di\Instance), ''trntv\\filekit\\S...'')
#5 /app/vendor/yii2-starter-kit/yii2-file-kit/src/behaviors/UploadBehavior.php(396): trntv\filekit\behaviors\UploadBehavior->getStorage()
#6 /app/vendor/yii2-starter-kit/yii2-file-kit/src/behaviors/UploadBehavior.php(273): trntv\filekit\behaviors\UploadBehavior->enrichFileData(Array)
#7 [internal function]: trntv\filekit\behaviors\UploadBehavior->afterFindSingle(Object(yii\base\Event))
#8 /app/vendor/yiisoft/yii2/base/Component.php(633): call_user_func(Array, Object(yii\base\Event))
#9 /app/vendor/yiisoft/yii2/db/BaseActiveRecord.php(942): yii\base\Component->trigger(''afterFind'')
#10 /app/vendor/yiisoft/yii2/db/ActiveQuery.php(233): yii\db\BaseActiveRecord->afterFind()
#11 /app/vendor/yiisoft/yii2/db/Query.php(251): yii\db\ActiveQuery->populate(Array)
#12 /app/vendor/yiisoft/yii2/db/ActiveQuery.php(133): yii\db\Query->all(NULL)
#13 /app/common/widgets/DbCarousel.php(65): yii\db\ActiveQuery->all()
#14 /app/vendor/yiisoft/yii2/base/BaseObject.php(109): common\widgets\DbCarousel->init()
#15 [internal function]: yii\base\BaseObject->__construct(Array)
#16 /app/vendor/yiisoft/yii2/di/Container.php(419): ReflectionClass->newInstanceArgs(Array)
#17 /app/vendor/yiisoft/yii2/di/Container.php(170): yii\di\Container->build(''common\\widgets\\...'', Array, Array)
#18 /app/vendor/yiisoft/yii2/BaseYii.php(365): yii\di\Container->get(''common\\widgets\\...'', Array, Array)
#19 /app/vendor/yiisoft/yii2/base/Widget.php(143): yii\BaseYii::createObject(Array)
#20 /app/frontend/views/site/index.php(10): yii\base\Widget::widget(Array)
#21 /app/vendor/yiisoft/yii2/base/View.php(347): require(''/app/frontend/v...'')
#22 /app/vendor/yiisoft/yii2/base/View.php(257): yii\base\View->renderPhpFile(''/app/frontend/v...'', Array)
#23 /app/vendor/yiisoft/yii2/base/View.php(156): yii\base\View->renderFile(''/app/frontend/v...'', Array, Object(frontend\controllers\SiteController))
#24 /app/vendor/yiisoft/yii2/base/Controller.php(407): yii\base\View->render(''index'', Array, Object(frontend\controllers\SiteController))
#25 /app/frontend/controllers/SiteController.php(60): yii\base\Controller->render(''index'')
#26 [internal function]: frontend\controllers\SiteController->actionIndex()
#27 /app/vendor/yiisoft/yii2/base/InlineAction.php(57): call_user_func_array(Array, Array)
#28 /app/vendor/yiisoft/yii2/base/Controller.php(178): yii\base\InlineAction->runWithParams(Array)
#29 /app/vendor/yiisoft/yii2/base/Module.php(552): yii\base\Controller->runAction(''index'', Array)
#30 /app/vendor/yiisoft/yii2/web/Application.php(103): yii\base\Module->runAction('''', Array)
#31 /app/vendor/yiisoft/yii2/base/Application.php(384): yii\web\Application->handleRequest(Object(yii\web\Request))
#32 /app/frontend/web/index.php(22): yii\base\Application->run()
#33 {main}')";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1752498192.224233;i:4;a:0:{}i:5;i:2317728;}}}";s:9:"profiling";s:50521:"a:3:{s:6:"memory";i:3119896;s:4:"time";d:4.0936689376831055;s:8:"messages";a:22:{i:7;a:6:{i:0;s:65:"Opening DB connection: pgsql:host=db;port=5432;dbname=skillhub_db";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1752498189.508067;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:71;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:113;s:8:"function";s:8:"getModel";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:28:"/app/frontend/config/web.php";s:4:"line";i:46;s:8:"function";s:3:"get";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}}i:5;i:1786920;}i:8;a:6:{i:0;s:65:"Opening DB connection: pgsql:host=db;port=5432;dbname=skillhub_db";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1752498189.600923;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:71;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:113;s:8:"function";s:8:"getModel";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:28:"/app/frontend/config/web.php";s:4:"line";i:46;s:8:"function";s:3:"get";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}}i:5;i:1789664;}i:10;a:6:{i:0;s:67:"SELECT * FROM "key_storage_item" WHERE "key"='frontend.maintenance'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752498189.60241;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:71;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:113;s:8:"function";s:8:"getModel";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:28:"/app/frontend/config/web.php";s:4:"line";i:46;s:8:"function";s:3:"get";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}}i:5;i:1792384;}i:11;a:6:{i:0;s:67:"SELECT * FROM "key_storage_item" WHERE "key"='frontend.maintenance'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752498189.64533;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:71;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:113;s:8:"function";s:8:"getModel";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:28:"/app/frontend/config/web.php";s:4:"line";i:46;s:8:"function";s:3:"get";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}}i:5;i:1795184;}i:13;a:6:{i:0;s:2822:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'key_storage_item'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752498189.720016;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:71;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:113;s:8:"function";s:8:"getModel";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:28:"/app/frontend/config/web.php";s:4:"line";i:46;s:8:"function";s:3:"get";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}}i:5;i:1823504;}i:14;a:6:{i:0;s:2822:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'key_storage_item'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752498189.787529;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:71;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:113;s:8:"function";s:8:"getModel";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:28:"/app/frontend/config/web.php";s:4:"line";i:46;s:8:"function";s:3:"get";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}}i:5;i:1834976;}i:16;a:6:{i:0;s:884:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='key_storage_item'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752498189.82735;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:71;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:113;s:8:"function";s:8:"getModel";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:28:"/app/frontend/config/web.php";s:4:"line";i:46;s:8:"function";s:3:"get";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}}i:5;i:1832752;}i:17;a:6:{i:0;s:884:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='key_storage_item'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752498189.831794;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:71;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:113;s:8:"function";s:8:"getModel";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:28:"/app/frontend/config/web.php";s:4:"line";i:46;s:8:"function";s:3:"get";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}}i:5;i:1835712;}i:37;a:6:{i:0;s:284:"SELECT "widget_carousel_item".* FROM "widget_carousel_item" LEFT JOIN "widget_carousel" ON "widget_carousel_item"."carousel_id" = "widget_carousel"."id" WHERE ("widget_carousel_item"."status"=1) AND ("widget_carousel"."status"=1) AND ("widget_carousel"."key"='index') ORDER BY "order"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752498190.413323;i:4;a:3:{i:0;a:5:{s:4:"file";s:34:"/app/common/widgets/DbCarousel.php";s:4:"line";i:65;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:34:"/app/frontend/views/site/index.php";s:4:"line";i:10;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"/app/frontend/controllers/SiteController.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:2147192;}i:38;a:6:{i:0;s:284:"SELECT "widget_carousel_item".* FROM "widget_carousel_item" LEFT JOIN "widget_carousel" ON "widget_carousel_item"."carousel_id" = "widget_carousel"."id" WHERE ("widget_carousel_item"."status"=1) AND ("widget_carousel"."status"=1) AND ("widget_carousel"."key"='index') ORDER BY "order"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752498190.419198;i:4;a:3:{i:0;a:5:{s:4:"file";s:34:"/app/common/widgets/DbCarousel.php";s:4:"line";i:65;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:34:"/app/frontend/views/site/index.php";s:4:"line";i:10;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"/app/frontend/controllers/SiteController.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:2151264;}i:40;a:6:{i:0;s:2826:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'widget_carousel_item'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752498190.419707;i:4;a:3:{i:0;a:5:{s:4:"file";s:34:"/app/common/widgets/DbCarousel.php";s:4:"line";i:65;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:34:"/app/frontend/views/site/index.php";s:4:"line";i:10;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"/app/frontend/controllers/SiteController.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:2183864;}i:41;a:6:{i:0;s:2826:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'widget_carousel_item'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752498190.4355;i:4;a:3:{i:0;a:5:{s:4:"file";s:34:"/app/common/widgets/DbCarousel.php";s:4:"line";i:65;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:34:"/app/frontend/views/site/index.php";s:4:"line";i:10;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"/app/frontend/controllers/SiteController.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:2206912;}i:43;a:6:{i:0;s:888:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='widget_carousel_item'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752498190.435888;i:4;a:3:{i:0;a:5:{s:4:"file";s:34:"/app/common/widgets/DbCarousel.php";s:4:"line";i:65;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:34:"/app/frontend/views/site/index.php";s:4:"line";i:10;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"/app/frontend/controllers/SiteController.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:2190736;}i:44;a:6:{i:0;s:888:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='widget_carousel_item'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752498190.438719;i:4;a:3:{i:0;a:5:{s:4:"file";s:34:"/app/common/widgets/DbCarousel.php";s:4:"line";i:65;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:34:"/app/frontend/views/site/index.php";s:4:"line";i:10;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"/app/frontend/controllers/SiteController.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:2193944;}i:46;a:6:{i:0;s:44:"SELECT * FROM "widget_carousel" WHERE "id"=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752498190.463452;i:4;a:3:{i:0;a:5:{s:4:"file";s:34:"/app/common/widgets/DbCarousel.php";s:4:"line";i:65;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:34:"/app/frontend/views/site/index.php";s:4:"line";i:10;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"/app/frontend/controllers/SiteController.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:2200544;}i:47;a:6:{i:0;s:44:"SELECT * FROM "widget_carousel" WHERE "id"=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752498190.46441;i:4;a:3:{i:0;a:5:{s:4:"file";s:34:"/app/common/widgets/DbCarousel.php";s:4:"line";i:65;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:34:"/app/frontend/views/site/index.php";s:4:"line";i:10;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"/app/frontend/controllers/SiteController.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:2203064;}i:49;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'widget_carousel'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752498190.464959;i:4;a:3:{i:0;a:5:{s:4:"file";s:34:"/app/common/widgets/DbCarousel.php";s:4:"line";i:65;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:34:"/app/frontend/views/site/index.php";s:4:"line";i:10;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"/app/frontend/controllers/SiteController.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:2227360;}i:50;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'widget_carousel'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752498190.475416;i:4;a:3:{i:0;a:5:{s:4:"file";s:34:"/app/common/widgets/DbCarousel.php";s:4:"line";i:65;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:34:"/app/frontend/views/site/index.php";s:4:"line";i:10;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"/app/frontend/controllers/SiteController.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:2235856;}i:52;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='widget_carousel'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752498190.47592;i:4;a:3:{i:0;a:5:{s:4:"file";s:34:"/app/common/widgets/DbCarousel.php";s:4:"line";i:65;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:34:"/app/frontend/views/site/index.php";s:4:"line";i:10;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"/app/frontend/controllers/SiteController.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:2229800;}i:53;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='widget_carousel'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752498190.480198;i:4;a:3:{i:0;a:5:{s:4:"file";s:34:"/app/common/widgets/DbCarousel.php";s:4:"line";i:65;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:34:"/app/frontend/views/site/index.php";s:4:"line";i:10;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"/app/frontend/controllers/SiteController.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:2232120;}i:92;a:6:{i:0;s:7157:"INSERT INTO "system_log" ("level", "category", "log_time", "prefix", "message")
                VALUES (1, 'yii\di\NotInstantiableException', 1752498190.6678, '[frontend][/]', 'ReflectionException: Class "fileStorage" does not exist in /app/vendor/yiisoft/yii2/di/Container.php:507
Stack trace:
#0 /app/vendor/yiisoft/yii2/di/Container.php(507): ReflectionClass->__construct(''fileStorage'')
#1 /app/vendor/yiisoft/yii2/di/Container.php(385): yii\di\Container->getDependencies(''fileStorage'')
#2 /app/vendor/yiisoft/yii2/di/Container.php(170): yii\di\Container->build(''fileStorage'', Array, Array)
#3 /app/vendor/yiisoft/yii2/di/Instance.php(175): yii\di\Container->get(''fileStorage'')
#4 /app/vendor/yiisoft/yii2/di/Instance.php(144): yii\di\Instance->get(NULL)
#5 /app/vendor/yii2-starter-kit/yii2-file-kit/src/behaviors/UploadBehavior.php(328): yii\di\Instance::ensure(Object(yii\di\Instance), ''trntv\\filekit\\S...'')
#6 /app/vendor/yii2-starter-kit/yii2-file-kit/src/behaviors/UploadBehavior.php(396): trntv\filekit\behaviors\UploadBehavior->getStorage()
#7 /app/vendor/yii2-starter-kit/yii2-file-kit/src/behaviors/UploadBehavior.php(273): trntv\filekit\behaviors\UploadBehavior->enrichFileData(Array)
#8 [internal function]: trntv\filekit\behaviors\UploadBehavior->afterFindSingle(Object(yii\base\Event))
#9 /app/vendor/yiisoft/yii2/base/Component.php(633): call_user_func(Array, Object(yii\base\Event))
#10 /app/vendor/yiisoft/yii2/db/BaseActiveRecord.php(942): yii\base\Component->trigger(''afterFind'')
#11 /app/vendor/yiisoft/yii2/db/ActiveQuery.php(233): yii\db\BaseActiveRecord->afterFind()
#12 /app/vendor/yiisoft/yii2/db/Query.php(251): yii\db\ActiveQuery->populate(Array)
#13 /app/vendor/yiisoft/yii2/db/ActiveQuery.php(133): yii\db\Query->all(NULL)
#14 /app/common/widgets/DbCarousel.php(65): yii\db\ActiveQuery->all()
#15 /app/vendor/yiisoft/yii2/base/BaseObject.php(109): common\widgets\DbCarousel->init()
#16 [internal function]: yii\base\BaseObject->__construct(Array)
#17 /app/vendor/yiisoft/yii2/di/Container.php(419): ReflectionClass->newInstanceArgs(Array)
#18 /app/vendor/yiisoft/yii2/di/Container.php(170): yii\di\Container->build(''common\\widgets\\...'', Array, Array)
#19 /app/vendor/yiisoft/yii2/BaseYii.php(365): yii\di\Container->get(''common\\widgets\\...'', Array, Array)
#20 /app/vendor/yiisoft/yii2/base/Widget.php(143): yii\BaseYii::createObject(Array)
#21 /app/frontend/views/site/index.php(10): yii\base\Widget::widget(Array)
#22 /app/vendor/yiisoft/yii2/base/View.php(347): require(''/app/frontend/v...'')
#23 /app/vendor/yiisoft/yii2/base/View.php(257): yii\base\View->renderPhpFile(''/app/frontend/v...'', Array)
#24 /app/vendor/yiisoft/yii2/base/View.php(156): yii\base\View->renderFile(''/app/frontend/v...'', Array, Object(frontend\controllers\SiteController))
#25 /app/vendor/yiisoft/yii2/base/Controller.php(407): yii\base\View->render(''index'', Array, Object(frontend\controllers\SiteController))
#26 /app/frontend/controllers/SiteController.php(60): yii\base\Controller->render(''index'')
#27 [internal function]: frontend\controllers\SiteController->actionIndex()
#28 /app/vendor/yiisoft/yii2/base/InlineAction.php(57): call_user_func_array(Array, Array)
#29 /app/vendor/yiisoft/yii2/base/Controller.php(178): yii\base\InlineAction->runWithParams(Array)
#30 /app/vendor/yiisoft/yii2/base/Module.php(552): yii\base\Controller->runAction(''index'', Array)
#31 /app/vendor/yiisoft/yii2/web/Application.php(103): yii\base\Module->runAction('''', Array)
#32 /app/vendor/yiisoft/yii2/base/Application.php(384): yii\web\Application->handleRequest(Object(yii\web\Request))
#33 /app/frontend/web/index.php(22): yii\base\Application->run()
#34 {main}

Next yii\di\NotInstantiableException: Failed to instantiate component or class "fileStorage". in /app/vendor/yiisoft/yii2/di/Container.php:509
Stack trace:
#0 /app/vendor/yiisoft/yii2/di/Container.php(385): yii\di\Container->getDependencies(''fileStorage'')
#1 /app/vendor/yiisoft/yii2/di/Container.php(170): yii\di\Container->build(''fileStorage'', Array, Array)
#2 /app/vendor/yiisoft/yii2/di/Instance.php(175): yii\di\Container->get(''fileStorage'')
#3 /app/vendor/yiisoft/yii2/di/Instance.php(144): yii\di\Instance->get(NULL)
#4 /app/vendor/yii2-starter-kit/yii2-file-kit/src/behaviors/UploadBehavior.php(328): yii\di\Instance::ensure(Object(yii\di\Instance), ''trntv\\filekit\\S...'')
#5 /app/vendor/yii2-starter-kit/yii2-file-kit/src/behaviors/UploadBehavior.php(396): trntv\filekit\behaviors\UploadBehavior->getStorage()
#6 /app/vendor/yii2-starter-kit/yii2-file-kit/src/behaviors/UploadBehavior.php(273): trntv\filekit\behaviors\UploadBehavior->enrichFileData(Array)
#7 [internal function]: trntv\filekit\behaviors\UploadBehavior->afterFindSingle(Object(yii\base\Event))
#8 /app/vendor/yiisoft/yii2/base/Component.php(633): call_user_func(Array, Object(yii\base\Event))
#9 /app/vendor/yiisoft/yii2/db/BaseActiveRecord.php(942): yii\base\Component->trigger(''afterFind'')
#10 /app/vendor/yiisoft/yii2/db/ActiveQuery.php(233): yii\db\BaseActiveRecord->afterFind()
#11 /app/vendor/yiisoft/yii2/db/Query.php(251): yii\db\ActiveQuery->populate(Array)
#12 /app/vendor/yiisoft/yii2/db/ActiveQuery.php(133): yii\db\Query->all(NULL)
#13 /app/common/widgets/DbCarousel.php(65): yii\db\ActiveQuery->all()
#14 /app/vendor/yiisoft/yii2/base/BaseObject.php(109): common\widgets\DbCarousel->init()
#15 [internal function]: yii\base\BaseObject->__construct(Array)
#16 /app/vendor/yiisoft/yii2/di/Container.php(419): ReflectionClass->newInstanceArgs(Array)
#17 /app/vendor/yiisoft/yii2/di/Container.php(170): yii\di\Container->build(''common\\widgets\\...'', Array, Array)
#18 /app/vendor/yiisoft/yii2/BaseYii.php(365): yii\di\Container->get(''common\\widgets\\...'', Array, Array)
#19 /app/vendor/yiisoft/yii2/base/Widget.php(143): yii\BaseYii::createObject(Array)
#20 /app/frontend/views/site/index.php(10): yii\base\Widget::widget(Array)
#21 /app/vendor/yiisoft/yii2/base/View.php(347): require(''/app/frontend/v...'')
#22 /app/vendor/yiisoft/yii2/base/View.php(257): yii\base\View->renderPhpFile(''/app/frontend/v...'', Array)
#23 /app/vendor/yiisoft/yii2/base/View.php(156): yii\base\View->renderFile(''/app/frontend/v...'', Array, Object(frontend\controllers\SiteController))
#24 /app/vendor/yiisoft/yii2/base/Controller.php(407): yii\base\View->render(''index'', Array, Object(frontend\controllers\SiteController))
#25 /app/frontend/controllers/SiteController.php(60): yii\base\Controller->render(''index'')
#26 [internal function]: frontend\controllers\SiteController->actionIndex()
#27 /app/vendor/yiisoft/yii2/base/InlineAction.php(57): call_user_func_array(Array, Array)
#28 /app/vendor/yiisoft/yii2/base/Controller.php(178): yii\base\InlineAction->runWithParams(Array)
#29 /app/vendor/yiisoft/yii2/base/Module.php(552): yii\base\Controller->runAction(''index'', Array)
#30 /app/vendor/yiisoft/yii2/web/Application.php(103): yii\base\Module->runAction('''', Array)
#31 /app/vendor/yiisoft/yii2/base/Application.php(384): yii\web\Application->handleRequest(Object(yii\web\Request))
#32 /app/frontend/web/index.php(22): yii\base\Application->run()
#33 {main}')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1752498192.22426;i:4;a:0:{}i:5;i:2319304;}i:93;a:6:{i:0;s:7157:"INSERT INTO "system_log" ("level", "category", "log_time", "prefix", "message")
                VALUES (1, 'yii\di\NotInstantiableException', 1752498190.6678, '[frontend][/]', 'ReflectionException: Class "fileStorage" does not exist in /app/vendor/yiisoft/yii2/di/Container.php:507
Stack trace:
#0 /app/vendor/yiisoft/yii2/di/Container.php(507): ReflectionClass->__construct(''fileStorage'')
#1 /app/vendor/yiisoft/yii2/di/Container.php(385): yii\di\Container->getDependencies(''fileStorage'')
#2 /app/vendor/yiisoft/yii2/di/Container.php(170): yii\di\Container->build(''fileStorage'', Array, Array)
#3 /app/vendor/yiisoft/yii2/di/Instance.php(175): yii\di\Container->get(''fileStorage'')
#4 /app/vendor/yiisoft/yii2/di/Instance.php(144): yii\di\Instance->get(NULL)
#5 /app/vendor/yii2-starter-kit/yii2-file-kit/src/behaviors/UploadBehavior.php(328): yii\di\Instance::ensure(Object(yii\di\Instance), ''trntv\\filekit\\S...'')
#6 /app/vendor/yii2-starter-kit/yii2-file-kit/src/behaviors/UploadBehavior.php(396): trntv\filekit\behaviors\UploadBehavior->getStorage()
#7 /app/vendor/yii2-starter-kit/yii2-file-kit/src/behaviors/UploadBehavior.php(273): trntv\filekit\behaviors\UploadBehavior->enrichFileData(Array)
#8 [internal function]: trntv\filekit\behaviors\UploadBehavior->afterFindSingle(Object(yii\base\Event))
#9 /app/vendor/yiisoft/yii2/base/Component.php(633): call_user_func(Array, Object(yii\base\Event))
#10 /app/vendor/yiisoft/yii2/db/BaseActiveRecord.php(942): yii\base\Component->trigger(''afterFind'')
#11 /app/vendor/yiisoft/yii2/db/ActiveQuery.php(233): yii\db\BaseActiveRecord->afterFind()
#12 /app/vendor/yiisoft/yii2/db/Query.php(251): yii\db\ActiveQuery->populate(Array)
#13 /app/vendor/yiisoft/yii2/db/ActiveQuery.php(133): yii\db\Query->all(NULL)
#14 /app/common/widgets/DbCarousel.php(65): yii\db\ActiveQuery->all()
#15 /app/vendor/yiisoft/yii2/base/BaseObject.php(109): common\widgets\DbCarousel->init()
#16 [internal function]: yii\base\BaseObject->__construct(Array)
#17 /app/vendor/yiisoft/yii2/di/Container.php(419): ReflectionClass->newInstanceArgs(Array)
#18 /app/vendor/yiisoft/yii2/di/Container.php(170): yii\di\Container->build(''common\\widgets\\...'', Array, Array)
#19 /app/vendor/yiisoft/yii2/BaseYii.php(365): yii\di\Container->get(''common\\widgets\\...'', Array, Array)
#20 /app/vendor/yiisoft/yii2/base/Widget.php(143): yii\BaseYii::createObject(Array)
#21 /app/frontend/views/site/index.php(10): yii\base\Widget::widget(Array)
#22 /app/vendor/yiisoft/yii2/base/View.php(347): require(''/app/frontend/v...'')
#23 /app/vendor/yiisoft/yii2/base/View.php(257): yii\base\View->renderPhpFile(''/app/frontend/v...'', Array)
#24 /app/vendor/yiisoft/yii2/base/View.php(156): yii\base\View->renderFile(''/app/frontend/v...'', Array, Object(frontend\controllers\SiteController))
#25 /app/vendor/yiisoft/yii2/base/Controller.php(407): yii\base\View->render(''index'', Array, Object(frontend\controllers\SiteController))
#26 /app/frontend/controllers/SiteController.php(60): yii\base\Controller->render(''index'')
#27 [internal function]: frontend\controllers\SiteController->actionIndex()
#28 /app/vendor/yiisoft/yii2/base/InlineAction.php(57): call_user_func_array(Array, Array)
#29 /app/vendor/yiisoft/yii2/base/Controller.php(178): yii\base\InlineAction->runWithParams(Array)
#30 /app/vendor/yiisoft/yii2/base/Module.php(552): yii\base\Controller->runAction(''index'', Array)
#31 /app/vendor/yiisoft/yii2/web/Application.php(103): yii\base\Module->runAction('''', Array)
#32 /app/vendor/yiisoft/yii2/base/Application.php(384): yii\web\Application->handleRequest(Object(yii\web\Request))
#33 /app/frontend/web/index.php(22): yii\base\Application->run()
#34 {main}

Next yii\di\NotInstantiableException: Failed to instantiate component or class "fileStorage". in /app/vendor/yiisoft/yii2/di/Container.php:509
Stack trace:
#0 /app/vendor/yiisoft/yii2/di/Container.php(385): yii\di\Container->getDependencies(''fileStorage'')
#1 /app/vendor/yiisoft/yii2/di/Container.php(170): yii\di\Container->build(''fileStorage'', Array, Array)
#2 /app/vendor/yiisoft/yii2/di/Instance.php(175): yii\di\Container->get(''fileStorage'')
#3 /app/vendor/yiisoft/yii2/di/Instance.php(144): yii\di\Instance->get(NULL)
#4 /app/vendor/yii2-starter-kit/yii2-file-kit/src/behaviors/UploadBehavior.php(328): yii\di\Instance::ensure(Object(yii\di\Instance), ''trntv\\filekit\\S...'')
#5 /app/vendor/yii2-starter-kit/yii2-file-kit/src/behaviors/UploadBehavior.php(396): trntv\filekit\behaviors\UploadBehavior->getStorage()
#6 /app/vendor/yii2-starter-kit/yii2-file-kit/src/behaviors/UploadBehavior.php(273): trntv\filekit\behaviors\UploadBehavior->enrichFileData(Array)
#7 [internal function]: trntv\filekit\behaviors\UploadBehavior->afterFindSingle(Object(yii\base\Event))
#8 /app/vendor/yiisoft/yii2/base/Component.php(633): call_user_func(Array, Object(yii\base\Event))
#9 /app/vendor/yiisoft/yii2/db/BaseActiveRecord.php(942): yii\base\Component->trigger(''afterFind'')
#10 /app/vendor/yiisoft/yii2/db/ActiveQuery.php(233): yii\db\BaseActiveRecord->afterFind()
#11 /app/vendor/yiisoft/yii2/db/Query.php(251): yii\db\ActiveQuery->populate(Array)
#12 /app/vendor/yiisoft/yii2/db/ActiveQuery.php(133): yii\db\Query->all(NULL)
#13 /app/common/widgets/DbCarousel.php(65): yii\db\ActiveQuery->all()
#14 /app/vendor/yiisoft/yii2/base/BaseObject.php(109): common\widgets\DbCarousel->init()
#15 [internal function]: yii\base\BaseObject->__construct(Array)
#16 /app/vendor/yiisoft/yii2/di/Container.php(419): ReflectionClass->newInstanceArgs(Array)
#17 /app/vendor/yiisoft/yii2/di/Container.php(170): yii\di\Container->build(''common\\widgets\\...'', Array, Array)
#18 /app/vendor/yiisoft/yii2/BaseYii.php(365): yii\di\Container->get(''common\\widgets\\...'', Array, Array)
#19 /app/vendor/yiisoft/yii2/base/Widget.php(143): yii\BaseYii::createObject(Array)
#20 /app/frontend/views/site/index.php(10): yii\base\Widget::widget(Array)
#21 /app/vendor/yiisoft/yii2/base/View.php(347): require(''/app/frontend/v...'')
#22 /app/vendor/yiisoft/yii2/base/View.php(257): yii\base\View->renderPhpFile(''/app/frontend/v...'', Array)
#23 /app/vendor/yiisoft/yii2/base/View.php(156): yii\base\View->renderFile(''/app/frontend/v...'', Array, Object(frontend\controllers\SiteController))
#24 /app/vendor/yiisoft/yii2/base/Controller.php(407): yii\base\View->render(''index'', Array, Object(frontend\controllers\SiteController))
#25 /app/frontend/controllers/SiteController.php(60): yii\base\Controller->render(''index'')
#26 [internal function]: frontend\controllers\SiteController->actionIndex()
#27 /app/vendor/yiisoft/yii2/base/InlineAction.php(57): call_user_func_array(Array, Array)
#28 /app/vendor/yiisoft/yii2/base/Controller.php(178): yii\base\InlineAction->runWithParams(Array)
#29 /app/vendor/yiisoft/yii2/base/Module.php(552): yii\base\Controller->runAction(''index'', Array)
#30 /app/vendor/yiisoft/yii2/web/Application.php(103): yii\base\Module->runAction('''', Array)
#31 /app/vendor/yiisoft/yii2/base/Application.php(384): yii\web\Application->handleRequest(Object(yii\web\Request))
#32 /app/frontend/web/index.php(22): yii\base\Application->run()
#33 {main}')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1752498192.240427;i:4;a:0:{}i:5;i:2319896;}}}";s:2:"db";s:48996:"a:1:{s:8:"messages";a:20:{i:10;a:6:{i:0;s:67:"SELECT * FROM "key_storage_item" WHERE "key"='frontend.maintenance'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752498189.60241;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:71;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:113;s:8:"function";s:8:"getModel";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:28:"/app/frontend/config/web.php";s:4:"line";i:46;s:8:"function";s:3:"get";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}}i:5;i:1792384;}i:11;a:6:{i:0;s:67:"SELECT * FROM "key_storage_item" WHERE "key"='frontend.maintenance'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752498189.64533;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:71;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:113;s:8:"function";s:8:"getModel";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:28:"/app/frontend/config/web.php";s:4:"line";i:46;s:8:"function";s:3:"get";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}}i:5;i:1795184;}i:13;a:6:{i:0;s:2822:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'key_storage_item'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752498189.720016;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:71;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:113;s:8:"function";s:8:"getModel";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:28:"/app/frontend/config/web.php";s:4:"line";i:46;s:8:"function";s:3:"get";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}}i:5;i:1823504;}i:14;a:6:{i:0;s:2822:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'key_storage_item'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752498189.787529;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:71;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:113;s:8:"function";s:8:"getModel";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:28:"/app/frontend/config/web.php";s:4:"line";i:46;s:8:"function";s:3:"get";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}}i:5;i:1834976;}i:16;a:6:{i:0;s:884:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='key_storage_item'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752498189.82735;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:71;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:113;s:8:"function";s:8:"getModel";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:28:"/app/frontend/config/web.php";s:4:"line";i:46;s:8:"function";s:3:"get";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}}i:5;i:1832752;}i:17;a:6:{i:0;s:884:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='key_storage_item'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752498189.831794;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:71;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:48:"/app/common/components/keyStorage/KeyStorage.php";s:4:"line";i:113;s:8:"function";s:8:"getModel";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:28:"/app/frontend/config/web.php";s:4:"line";i:46;s:8:"function";s:3:"get";s:5:"class";s:39:"common\components\keyStorage\KeyStorage";s:4:"type";s:2:"->";}}i:5;i:1835712;}i:37;a:6:{i:0;s:284:"SELECT "widget_carousel_item".* FROM "widget_carousel_item" LEFT JOIN "widget_carousel" ON "widget_carousel_item"."carousel_id" = "widget_carousel"."id" WHERE ("widget_carousel_item"."status"=1) AND ("widget_carousel"."status"=1) AND ("widget_carousel"."key"='index') ORDER BY "order"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752498190.413323;i:4;a:3:{i:0;a:5:{s:4:"file";s:34:"/app/common/widgets/DbCarousel.php";s:4:"line";i:65;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:34:"/app/frontend/views/site/index.php";s:4:"line";i:10;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"/app/frontend/controllers/SiteController.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:2147192;}i:38;a:6:{i:0;s:284:"SELECT "widget_carousel_item".* FROM "widget_carousel_item" LEFT JOIN "widget_carousel" ON "widget_carousel_item"."carousel_id" = "widget_carousel"."id" WHERE ("widget_carousel_item"."status"=1) AND ("widget_carousel"."status"=1) AND ("widget_carousel"."key"='index') ORDER BY "order"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752498190.419198;i:4;a:3:{i:0;a:5:{s:4:"file";s:34:"/app/common/widgets/DbCarousel.php";s:4:"line";i:65;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:34:"/app/frontend/views/site/index.php";s:4:"line";i:10;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"/app/frontend/controllers/SiteController.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:2151264;}i:40;a:6:{i:0;s:2826:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'widget_carousel_item'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752498190.419707;i:4;a:3:{i:0;a:5:{s:4:"file";s:34:"/app/common/widgets/DbCarousel.php";s:4:"line";i:65;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:34:"/app/frontend/views/site/index.php";s:4:"line";i:10;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"/app/frontend/controllers/SiteController.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:2183864;}i:41;a:6:{i:0;s:2826:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'widget_carousel_item'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752498190.4355;i:4;a:3:{i:0;a:5:{s:4:"file";s:34:"/app/common/widgets/DbCarousel.php";s:4:"line";i:65;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:34:"/app/frontend/views/site/index.php";s:4:"line";i:10;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"/app/frontend/controllers/SiteController.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:2206912;}i:43;a:6:{i:0;s:888:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='widget_carousel_item'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752498190.435888;i:4;a:3:{i:0;a:5:{s:4:"file";s:34:"/app/common/widgets/DbCarousel.php";s:4:"line";i:65;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:34:"/app/frontend/views/site/index.php";s:4:"line";i:10;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"/app/frontend/controllers/SiteController.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:2190736;}i:44;a:6:{i:0;s:888:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='widget_carousel_item'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752498190.438719;i:4;a:3:{i:0;a:5:{s:4:"file";s:34:"/app/common/widgets/DbCarousel.php";s:4:"line";i:65;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:34:"/app/frontend/views/site/index.php";s:4:"line";i:10;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"/app/frontend/controllers/SiteController.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:2193944;}i:46;a:6:{i:0;s:44:"SELECT * FROM "widget_carousel" WHERE "id"=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752498190.463452;i:4;a:3:{i:0;a:5:{s:4:"file";s:34:"/app/common/widgets/DbCarousel.php";s:4:"line";i:65;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:34:"/app/frontend/views/site/index.php";s:4:"line";i:10;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"/app/frontend/controllers/SiteController.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:2200544;}i:47;a:6:{i:0;s:44:"SELECT * FROM "widget_carousel" WHERE "id"=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752498190.46441;i:4;a:3:{i:0;a:5:{s:4:"file";s:34:"/app/common/widgets/DbCarousel.php";s:4:"line";i:65;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:34:"/app/frontend/views/site/index.php";s:4:"line";i:10;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"/app/frontend/controllers/SiteController.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:2203064;}i:49;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'widget_carousel'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752498190.464959;i:4;a:3:{i:0;a:5:{s:4:"file";s:34:"/app/common/widgets/DbCarousel.php";s:4:"line";i:65;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:34:"/app/frontend/views/site/index.php";s:4:"line";i:10;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"/app/frontend/controllers/SiteController.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:2227360;}i:50;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'widget_carousel'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752498190.475416;i:4;a:3:{i:0;a:5:{s:4:"file";s:34:"/app/common/widgets/DbCarousel.php";s:4:"line";i:65;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:34:"/app/frontend/views/site/index.php";s:4:"line";i:10;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"/app/frontend/controllers/SiteController.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:2235856;}i:52;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='widget_carousel'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752498190.47592;i:4;a:3:{i:0;a:5:{s:4:"file";s:34:"/app/common/widgets/DbCarousel.php";s:4:"line";i:65;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:34:"/app/frontend/views/site/index.php";s:4:"line";i:10;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"/app/frontend/controllers/SiteController.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:2229800;}i:53;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='widget_carousel'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752498190.480198;i:4;a:3:{i:0;a:5:{s:4:"file";s:34:"/app/common/widgets/DbCarousel.php";s:4:"line";i:65;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:34:"/app/frontend/views/site/index.php";s:4:"line";i:10;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:44:"/app/frontend/controllers/SiteController.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:2232120;}i:92;a:6:{i:0;s:7157:"INSERT INTO "system_log" ("level", "category", "log_time", "prefix", "message")
                VALUES (1, 'yii\di\NotInstantiableException', 1752498190.6678, '[frontend][/]', 'ReflectionException: Class "fileStorage" does not exist in /app/vendor/yiisoft/yii2/di/Container.php:507
Stack trace:
#0 /app/vendor/yiisoft/yii2/di/Container.php(507): ReflectionClass->__construct(''fileStorage'')
#1 /app/vendor/yiisoft/yii2/di/Container.php(385): yii\di\Container->getDependencies(''fileStorage'')
#2 /app/vendor/yiisoft/yii2/di/Container.php(170): yii\di\Container->build(''fileStorage'', Array, Array)
#3 /app/vendor/yiisoft/yii2/di/Instance.php(175): yii\di\Container->get(''fileStorage'')
#4 /app/vendor/yiisoft/yii2/di/Instance.php(144): yii\di\Instance->get(NULL)
#5 /app/vendor/yii2-starter-kit/yii2-file-kit/src/behaviors/UploadBehavior.php(328): yii\di\Instance::ensure(Object(yii\di\Instance), ''trntv\\filekit\\S...'')
#6 /app/vendor/yii2-starter-kit/yii2-file-kit/src/behaviors/UploadBehavior.php(396): trntv\filekit\behaviors\UploadBehavior->getStorage()
#7 /app/vendor/yii2-starter-kit/yii2-file-kit/src/behaviors/UploadBehavior.php(273): trntv\filekit\behaviors\UploadBehavior->enrichFileData(Array)
#8 [internal function]: trntv\filekit\behaviors\UploadBehavior->afterFindSingle(Object(yii\base\Event))
#9 /app/vendor/yiisoft/yii2/base/Component.php(633): call_user_func(Array, Object(yii\base\Event))
#10 /app/vendor/yiisoft/yii2/db/BaseActiveRecord.php(942): yii\base\Component->trigger(''afterFind'')
#11 /app/vendor/yiisoft/yii2/db/ActiveQuery.php(233): yii\db\BaseActiveRecord->afterFind()
#12 /app/vendor/yiisoft/yii2/db/Query.php(251): yii\db\ActiveQuery->populate(Array)
#13 /app/vendor/yiisoft/yii2/db/ActiveQuery.php(133): yii\db\Query->all(NULL)
#14 /app/common/widgets/DbCarousel.php(65): yii\db\ActiveQuery->all()
#15 /app/vendor/yiisoft/yii2/base/BaseObject.php(109): common\widgets\DbCarousel->init()
#16 [internal function]: yii\base\BaseObject->__construct(Array)
#17 /app/vendor/yiisoft/yii2/di/Container.php(419): ReflectionClass->newInstanceArgs(Array)
#18 /app/vendor/yiisoft/yii2/di/Container.php(170): yii\di\Container->build(''common\\widgets\\...'', Array, Array)
#19 /app/vendor/yiisoft/yii2/BaseYii.php(365): yii\di\Container->get(''common\\widgets\\...'', Array, Array)
#20 /app/vendor/yiisoft/yii2/base/Widget.php(143): yii\BaseYii::createObject(Array)
#21 /app/frontend/views/site/index.php(10): yii\base\Widget::widget(Array)
#22 /app/vendor/yiisoft/yii2/base/View.php(347): require(''/app/frontend/v...'')
#23 /app/vendor/yiisoft/yii2/base/View.php(257): yii\base\View->renderPhpFile(''/app/frontend/v...'', Array)
#24 /app/vendor/yiisoft/yii2/base/View.php(156): yii\base\View->renderFile(''/app/frontend/v...'', Array, Object(frontend\controllers\SiteController))
#25 /app/vendor/yiisoft/yii2/base/Controller.php(407): yii\base\View->render(''index'', Array, Object(frontend\controllers\SiteController))
#26 /app/frontend/controllers/SiteController.php(60): yii\base\Controller->render(''index'')
#27 [internal function]: frontend\controllers\SiteController->actionIndex()
#28 /app/vendor/yiisoft/yii2/base/InlineAction.php(57): call_user_func_array(Array, Array)
#29 /app/vendor/yiisoft/yii2/base/Controller.php(178): yii\base\InlineAction->runWithParams(Array)
#30 /app/vendor/yiisoft/yii2/base/Module.php(552): yii\base\Controller->runAction(''index'', Array)
#31 /app/vendor/yiisoft/yii2/web/Application.php(103): yii\base\Module->runAction('''', Array)
#32 /app/vendor/yiisoft/yii2/base/Application.php(384): yii\web\Application->handleRequest(Object(yii\web\Request))
#33 /app/frontend/web/index.php(22): yii\base\Application->run()
#34 {main}

Next yii\di\NotInstantiableException: Failed to instantiate component or class "fileStorage". in /app/vendor/yiisoft/yii2/di/Container.php:509
Stack trace:
#0 /app/vendor/yiisoft/yii2/di/Container.php(385): yii\di\Container->getDependencies(''fileStorage'')
#1 /app/vendor/yiisoft/yii2/di/Container.php(170): yii\di\Container->build(''fileStorage'', Array, Array)
#2 /app/vendor/yiisoft/yii2/di/Instance.php(175): yii\di\Container->get(''fileStorage'')
#3 /app/vendor/yiisoft/yii2/di/Instance.php(144): yii\di\Instance->get(NULL)
#4 /app/vendor/yii2-starter-kit/yii2-file-kit/src/behaviors/UploadBehavior.php(328): yii\di\Instance::ensure(Object(yii\di\Instance), ''trntv\\filekit\\S...'')
#5 /app/vendor/yii2-starter-kit/yii2-file-kit/src/behaviors/UploadBehavior.php(396): trntv\filekit\behaviors\UploadBehavior->getStorage()
#6 /app/vendor/yii2-starter-kit/yii2-file-kit/src/behaviors/UploadBehavior.php(273): trntv\filekit\behaviors\UploadBehavior->enrichFileData(Array)
#7 [internal function]: trntv\filekit\behaviors\UploadBehavior->afterFindSingle(Object(yii\base\Event))
#8 /app/vendor/yiisoft/yii2/base/Component.php(633): call_user_func(Array, Object(yii\base\Event))
#9 /app/vendor/yiisoft/yii2/db/BaseActiveRecord.php(942): yii\base\Component->trigger(''afterFind'')
#10 /app/vendor/yiisoft/yii2/db/ActiveQuery.php(233): yii\db\BaseActiveRecord->afterFind()
#11 /app/vendor/yiisoft/yii2/db/Query.php(251): yii\db\ActiveQuery->populate(Array)
#12 /app/vendor/yiisoft/yii2/db/ActiveQuery.php(133): yii\db\Query->all(NULL)
#13 /app/common/widgets/DbCarousel.php(65): yii\db\ActiveQuery->all()
#14 /app/vendor/yiisoft/yii2/base/BaseObject.php(109): common\widgets\DbCarousel->init()
#15 [internal function]: yii\base\BaseObject->__construct(Array)
#16 /app/vendor/yiisoft/yii2/di/Container.php(419): ReflectionClass->newInstanceArgs(Array)
#17 /app/vendor/yiisoft/yii2/di/Container.php(170): yii\di\Container->build(''common\\widgets\\...'', Array, Array)
#18 /app/vendor/yiisoft/yii2/BaseYii.php(365): yii\di\Container->get(''common\\widgets\\...'', Array, Array)
#19 /app/vendor/yiisoft/yii2/base/Widget.php(143): yii\BaseYii::createObject(Array)
#20 /app/frontend/views/site/index.php(10): yii\base\Widget::widget(Array)
#21 /app/vendor/yiisoft/yii2/base/View.php(347): require(''/app/frontend/v...'')
#22 /app/vendor/yiisoft/yii2/base/View.php(257): yii\base\View->renderPhpFile(''/app/frontend/v...'', Array)
#23 /app/vendor/yiisoft/yii2/base/View.php(156): yii\base\View->renderFile(''/app/frontend/v...'', Array, Object(frontend\controllers\SiteController))
#24 /app/vendor/yiisoft/yii2/base/Controller.php(407): yii\base\View->render(''index'', Array, Object(frontend\controllers\SiteController))
#25 /app/frontend/controllers/SiteController.php(60): yii\base\Controller->render(''index'')
#26 [internal function]: frontend\controllers\SiteController->actionIndex()
#27 /app/vendor/yiisoft/yii2/base/InlineAction.php(57): call_user_func_array(Array, Array)
#28 /app/vendor/yiisoft/yii2/base/Controller.php(178): yii\base\InlineAction->runWithParams(Array)
#29 /app/vendor/yiisoft/yii2/base/Module.php(552): yii\base\Controller->runAction(''index'', Array)
#30 /app/vendor/yiisoft/yii2/web/Application.php(103): yii\base\Module->runAction('''', Array)
#31 /app/vendor/yiisoft/yii2/base/Application.php(384): yii\web\Application->handleRequest(Object(yii\web\Request))
#32 /app/frontend/web/index.php(22): yii\base\Application->run()
#33 {main}')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1752498192.22426;i:4;a:0:{}i:5;i:2319304;}i:93;a:6:{i:0;s:7157:"INSERT INTO "system_log" ("level", "category", "log_time", "prefix", "message")
                VALUES (1, 'yii\di\NotInstantiableException', 1752498190.6678, '[frontend][/]', 'ReflectionException: Class "fileStorage" does not exist in /app/vendor/yiisoft/yii2/di/Container.php:507
Stack trace:
#0 /app/vendor/yiisoft/yii2/di/Container.php(507): ReflectionClass->__construct(''fileStorage'')
#1 /app/vendor/yiisoft/yii2/di/Container.php(385): yii\di\Container->getDependencies(''fileStorage'')
#2 /app/vendor/yiisoft/yii2/di/Container.php(170): yii\di\Container->build(''fileStorage'', Array, Array)
#3 /app/vendor/yiisoft/yii2/di/Instance.php(175): yii\di\Container->get(''fileStorage'')
#4 /app/vendor/yiisoft/yii2/di/Instance.php(144): yii\di\Instance->get(NULL)
#5 /app/vendor/yii2-starter-kit/yii2-file-kit/src/behaviors/UploadBehavior.php(328): yii\di\Instance::ensure(Object(yii\di\Instance), ''trntv\\filekit\\S...'')
#6 /app/vendor/yii2-starter-kit/yii2-file-kit/src/behaviors/UploadBehavior.php(396): trntv\filekit\behaviors\UploadBehavior->getStorage()
#7 /app/vendor/yii2-starter-kit/yii2-file-kit/src/behaviors/UploadBehavior.php(273): trntv\filekit\behaviors\UploadBehavior->enrichFileData(Array)
#8 [internal function]: trntv\filekit\behaviors\UploadBehavior->afterFindSingle(Object(yii\base\Event))
#9 /app/vendor/yiisoft/yii2/base/Component.php(633): call_user_func(Array, Object(yii\base\Event))
#10 /app/vendor/yiisoft/yii2/db/BaseActiveRecord.php(942): yii\base\Component->trigger(''afterFind'')
#11 /app/vendor/yiisoft/yii2/db/ActiveQuery.php(233): yii\db\BaseActiveRecord->afterFind()
#12 /app/vendor/yiisoft/yii2/db/Query.php(251): yii\db\ActiveQuery->populate(Array)
#13 /app/vendor/yiisoft/yii2/db/ActiveQuery.php(133): yii\db\Query->all(NULL)
#14 /app/common/widgets/DbCarousel.php(65): yii\db\ActiveQuery->all()
#15 /app/vendor/yiisoft/yii2/base/BaseObject.php(109): common\widgets\DbCarousel->init()
#16 [internal function]: yii\base\BaseObject->__construct(Array)
#17 /app/vendor/yiisoft/yii2/di/Container.php(419): ReflectionClass->newInstanceArgs(Array)
#18 /app/vendor/yiisoft/yii2/di/Container.php(170): yii\di\Container->build(''common\\widgets\\...'', Array, Array)
#19 /app/vendor/yiisoft/yii2/BaseYii.php(365): yii\di\Container->get(''common\\widgets\\...'', Array, Array)
#20 /app/vendor/yiisoft/yii2/base/Widget.php(143): yii\BaseYii::createObject(Array)
#21 /app/frontend/views/site/index.php(10): yii\base\Widget::widget(Array)
#22 /app/vendor/yiisoft/yii2/base/View.php(347): require(''/app/frontend/v...'')
#23 /app/vendor/yiisoft/yii2/base/View.php(257): yii\base\View->renderPhpFile(''/app/frontend/v...'', Array)
#24 /app/vendor/yiisoft/yii2/base/View.php(156): yii\base\View->renderFile(''/app/frontend/v...'', Array, Object(frontend\controllers\SiteController))
#25 /app/vendor/yiisoft/yii2/base/Controller.php(407): yii\base\View->render(''index'', Array, Object(frontend\controllers\SiteController))
#26 /app/frontend/controllers/SiteController.php(60): yii\base\Controller->render(''index'')
#27 [internal function]: frontend\controllers\SiteController->actionIndex()
#28 /app/vendor/yiisoft/yii2/base/InlineAction.php(57): call_user_func_array(Array, Array)
#29 /app/vendor/yiisoft/yii2/base/Controller.php(178): yii\base\InlineAction->runWithParams(Array)
#30 /app/vendor/yiisoft/yii2/base/Module.php(552): yii\base\Controller->runAction(''index'', Array)
#31 /app/vendor/yiisoft/yii2/web/Application.php(103): yii\base\Module->runAction('''', Array)
#32 /app/vendor/yiisoft/yii2/base/Application.php(384): yii\web\Application->handleRequest(Object(yii\web\Request))
#33 /app/frontend/web/index.php(22): yii\base\Application->run()
#34 {main}

Next yii\di\NotInstantiableException: Failed to instantiate component or class "fileStorage". in /app/vendor/yiisoft/yii2/di/Container.php:509
Stack trace:
#0 /app/vendor/yiisoft/yii2/di/Container.php(385): yii\di\Container->getDependencies(''fileStorage'')
#1 /app/vendor/yiisoft/yii2/di/Container.php(170): yii\di\Container->build(''fileStorage'', Array, Array)
#2 /app/vendor/yiisoft/yii2/di/Instance.php(175): yii\di\Container->get(''fileStorage'')
#3 /app/vendor/yiisoft/yii2/di/Instance.php(144): yii\di\Instance->get(NULL)
#4 /app/vendor/yii2-starter-kit/yii2-file-kit/src/behaviors/UploadBehavior.php(328): yii\di\Instance::ensure(Object(yii\di\Instance), ''trntv\\filekit\\S...'')
#5 /app/vendor/yii2-starter-kit/yii2-file-kit/src/behaviors/UploadBehavior.php(396): trntv\filekit\behaviors\UploadBehavior->getStorage()
#6 /app/vendor/yii2-starter-kit/yii2-file-kit/src/behaviors/UploadBehavior.php(273): trntv\filekit\behaviors\UploadBehavior->enrichFileData(Array)
#7 [internal function]: trntv\filekit\behaviors\UploadBehavior->afterFindSingle(Object(yii\base\Event))
#8 /app/vendor/yiisoft/yii2/base/Component.php(633): call_user_func(Array, Object(yii\base\Event))
#9 /app/vendor/yiisoft/yii2/db/BaseActiveRecord.php(942): yii\base\Component->trigger(''afterFind'')
#10 /app/vendor/yiisoft/yii2/db/ActiveQuery.php(233): yii\db\BaseActiveRecord->afterFind()
#11 /app/vendor/yiisoft/yii2/db/Query.php(251): yii\db\ActiveQuery->populate(Array)
#12 /app/vendor/yiisoft/yii2/db/ActiveQuery.php(133): yii\db\Query->all(NULL)
#13 /app/common/widgets/DbCarousel.php(65): yii\db\ActiveQuery->all()
#14 /app/vendor/yiisoft/yii2/base/BaseObject.php(109): common\widgets\DbCarousel->init()
#15 [internal function]: yii\base\BaseObject->__construct(Array)
#16 /app/vendor/yiisoft/yii2/di/Container.php(419): ReflectionClass->newInstanceArgs(Array)
#17 /app/vendor/yiisoft/yii2/di/Container.php(170): yii\di\Container->build(''common\\widgets\\...'', Array, Array)
#18 /app/vendor/yiisoft/yii2/BaseYii.php(365): yii\di\Container->get(''common\\widgets\\...'', Array, Array)
#19 /app/vendor/yiisoft/yii2/base/Widget.php(143): yii\BaseYii::createObject(Array)
#20 /app/frontend/views/site/index.php(10): yii\base\Widget::widget(Array)
#21 /app/vendor/yiisoft/yii2/base/View.php(347): require(''/app/frontend/v...'')
#22 /app/vendor/yiisoft/yii2/base/View.php(257): yii\base\View->renderPhpFile(''/app/frontend/v...'', Array)
#23 /app/vendor/yiisoft/yii2/base/View.php(156): yii\base\View->renderFile(''/app/frontend/v...'', Array, Object(frontend\controllers\SiteController))
#24 /app/vendor/yiisoft/yii2/base/Controller.php(407): yii\base\View->render(''index'', Array, Object(frontend\controllers\SiteController))
#25 /app/frontend/controllers/SiteController.php(60): yii\base\Controller->render(''index'')
#26 [internal function]: frontend\controllers\SiteController->actionIndex()
#27 /app/vendor/yiisoft/yii2/base/InlineAction.php(57): call_user_func_array(Array, Array)
#28 /app/vendor/yiisoft/yii2/base/Controller.php(178): yii\base\InlineAction->runWithParams(Array)
#29 /app/vendor/yiisoft/yii2/base/Module.php(552): yii\base\Controller->runAction(''index'', Array)
#30 /app/vendor/yiisoft/yii2/web/Application.php(103): yii\base\Module->runAction('''', Array)
#31 /app/vendor/yiisoft/yii2/base/Application.php(384): yii\web\Application->handleRequest(Object(yii\web\Request))
#32 /app/frontend/web/index.php(22): yii\base\Application->run()
#33 {main}')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1752498192.240427;i:4;a:0:{}i:5;i:2319896;}}}";s:5:"event";s:16053:"a:93:{i:0;a:5:{s:4:"time";d:1752498189.394378;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:1;a:5:{s:4:"time";d:1752498189.60091;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:2;a:5:{s:4:"time";d:1752498189.691558;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\models\KeyStorageItem";}i:3;a:5:{s:4:"time";d:1752498189.832373;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\models\KeyStorageItem";}i:4;a:5:{s:4:"time";d:1752498189.863957;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:5;a:5:{s:4:"time";d:1752498189.913441;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:6;a:5:{s:4:"time";d:1752498190.089577;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"frontend\controllers\SiteController";}i:7;a:5:{s:4:"time";d:1752498190.117986;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:8;a:5:{s:4:"time";d:1752498190.297519;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:9;a:5:{s:4:"time";d:1752498190.411738;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"common\models\WidgetCarouselItem";}i:10;a:5:{s:4:"time";d:1752498190.412154;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:11;a:5:{s:4:"time";d:1752498190.41954;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"common\models\WidgetCarouselItem";}i:12;a:5:{s:4:"time";d:1752498190.439185;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:13;a:5:{s:4:"time";d:1752498190.464805;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\models\WidgetCarousel";}i:14;a:5:{s:4:"time";d:1752498190.480602;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\models\WidgetCarousel";}i:15;a:5:{s:4:"time";d:1752498190.680947;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:16;a:5:{s:4:"time";d:1752498190.979789;s:4:"name";s:9:"beginPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:17;a:5:{s:4:"time";d:1752498190.991291;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:18;a:5:{s:4:"time";d:1752498191.06862;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:19;a:5:{s:4:"time";d:1752498191.087086;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:20;a:5:{s:4:"time";d:1752498191.204384;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:21;a:5:{s:4:"time";d:1752498191.221825;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:22;a:5:{s:4:"time";d:1752498191.223194;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:23;a:5:{s:4:"time";d:1752498191.237217;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:24;a:5:{s:4:"time";d:1752498191.237332;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:25;a:5:{s:4:"time";d:1752498191.250911;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:26;a:5:{s:4:"time";d:1752498191.251007;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:27;a:5:{s:4:"time";d:1752498191.262744;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:28;a:5:{s:4:"time";d:1752498191.262849;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:29;a:5:{s:4:"time";d:1752498191.277791;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:30;a:5:{s:4:"time";d:1752498191.277897;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:31;a:5:{s:4:"time";d:1752498191.291714;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:32;a:5:{s:4:"time";d:1752498191.291856;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:33;a:5:{s:4:"time";d:1752498191.305017;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:34;a:5:{s:4:"time";d:1752498191.305121;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:35;a:5:{s:4:"time";d:1752498191.312267;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:36;a:5:{s:4:"time";d:1752498191.312342;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:37;a:5:{s:4:"time";d:1752498191.329674;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:38;a:5:{s:4:"time";d:1752498191.329756;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:39;a:5:{s:4:"time";d:1752498191.379794;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:40;a:5:{s:4:"time";d:1752498191.379892;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:41;a:5:{s:4:"time";d:1752498191.437005;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:42;a:5:{s:4:"time";d:1752498191.437096;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:43;a:5:{s:4:"time";d:1752498191.478676;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:44;a:5:{s:4:"time";d:1752498191.478784;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:45;a:5:{s:4:"time";d:1752498191.49399;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:46;a:5:{s:4:"time";d:1752498191.494082;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:47;a:5:{s:4:"time";d:1752498191.512043;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:48;a:5:{s:4:"time";d:1752498191.512188;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:49;a:5:{s:4:"time";d:1752498191.528501;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:50;a:5:{s:4:"time";d:1752498191.528597;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:51;a:5:{s:4:"time";d:1752498191.535271;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:52;a:5:{s:4:"time";d:1752498191.535384;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:53;a:5:{s:4:"time";d:1752498191.550484;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:54;a:5:{s:4:"time";d:1752498191.550585;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:55;a:5:{s:4:"time";d:1752498191.563212;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:56;a:5:{s:4:"time";d:1752498191.563314;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:57;a:5:{s:4:"time";d:1752498191.580581;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:58;a:5:{s:4:"time";d:1752498191.580693;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:59;a:5:{s:4:"time";d:1752498191.590971;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:60;a:5:{s:4:"time";d:1752498191.591069;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:61;a:5:{s:4:"time";d:1752498191.602094;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:62;a:5:{s:4:"time";d:1752498191.602207;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:63;a:5:{s:4:"time";d:1752498191.623261;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:64;a:5:{s:4:"time";d:1752498191.623378;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:65;a:5:{s:4:"time";d:1752498191.642504;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:66;a:5:{s:4:"time";d:1752498191.642645;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:67;a:5:{s:4:"time";d:1752498191.65954;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:68;a:5:{s:4:"time";d:1752498191.659656;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:69;a:5:{s:4:"time";d:1752498191.678486;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:70;a:5:{s:4:"time";d:1752498191.678652;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:71;a:5:{s:4:"time";d:1752498191.691315;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:72;a:5:{s:4:"time";d:1752498191.691431;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:73;a:5:{s:4:"time";d:1752498191.698815;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:74;a:5:{s:4:"time";d:1752498191.698922;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:75;a:5:{s:4:"time";d:1752498191.719193;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:76;a:5:{s:4:"time";d:1752498191.719279;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:77;a:5:{s:4:"time";d:1752498191.750035;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:78;a:5:{s:4:"time";d:1752498191.750188;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:79;a:5:{s:4:"time";d:1752498191.783292;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:80;a:5:{s:4:"time";d:1752498191.783449;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:81;a:5:{s:4:"time";d:1752498191.828754;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:82;a:5:{s:4:"time";d:1752498191.828886;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:83;a:5:{s:4:"time";d:1752498191.842338;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:84;a:5:{s:4:"time";d:1752498191.842447;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:85;a:5:{s:4:"time";d:1752498191.848273;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:86;a:5:{s:4:"time";d:1752498191.848356;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:87;a:5:{s:4:"time";d:1752498192.153672;s:4:"name";s:7:"endBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:88;a:5:{s:4:"time";d:1752498192.153691;s:4:"name";s:7:"endPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:89;a:5:{s:4:"time";d:1752498192.154761;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:90;a:5:{s:4:"time";d:1752498192.154775;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:91;a:5:{s:4:"time";d:1752498192.22107;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:92;a:5:{s:4:"time";d:1752498192.222893;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:91:"a:3:{s:5:"start";d:1752498188.353593;s:3:"end";d:1752498192.447513;s:6:"memory";i:3119896;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:2562:"a:3:{s:8:"messages";a:13:{i:19;a:6:{i:0;a:3:{s:4:"rule";s:5:"debug";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752498189.864677;i:4;a:0:{}i:5;i:1907384;}i:20;a:6:{i:0;a:3:{s:4:"rule";s:43:"debug/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752498189.864859;i:4;a:0:{}i:5;i:1908136;}i:21;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752498189.864869;i:4;a:0:{}i:5;i:1908888;}i:22;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752498189.864909;i:4;a:0:{}i:5;i:1909640;}i:23;a:6:{i:0;a:3:{s:4:"rule";s:41:"gii/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752498189.864916;i:4;a:0:{}i:5;i:1910392;}i:24;a:6:{i:0;a:3:{s:4:"rule";s:11:"page/<slug>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752498189.867858;i:4;a:0:{}i:5;i:1911144;}i:25;a:6:{i:0;a:3:{s:4:"rule";s:13:"article/index";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752498189.867957;i:4;a:0:{}i:5;i:1911896;}i:26;a:6:{i:0;a:3:{s:4:"rule";s:27:"article/attachment-download";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752498189.867976;i:4;a:0:{}i:5;i:1912648;}i:27;a:6:{i:0;a:3:{s:4:"rule";s:14:"article/<slug>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752498189.867995;i:4;a:0:{}i:5;i:1913400;}i:28;a:6:{i:0;a:3:{s:4:"rule";s:11:"sitemap.xml";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752498189.868003;i:4;a:0:{}i:5;i:1914152;}i:29;a:6:{i:0;a:3:{s:4:"rule";s:11:"sitemap.txt";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752498189.868009;i:4;a:0:{}i:5;i:1914904;}i:30;a:6:{i:0;a:3:{s:4:"rule";s:14:"sitemap.xml.gz";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752498189.868017;i:4;a:0:{}i:5;i:1915656;}i:31;a:6:{i:0;s:55:"No matching URL rules. Using default URL parsing logic.";i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752498189.868019;i:4;a:0:{}i:5;i:1916032;}}s:5:"route";s:10:"site/index";s:6:"action";s:50:"frontend\controllers\SiteController::actionIndex()";}";s:7:"request";s:5644:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:500;s:14:"requestHeaders";a:10:{s:15:"accept-language";s:35:"ru-RU,ru;q=0.9,en-US;q=0.8,en;q=0.7";s:15:"accept-encoding";s:13:"gzip, deflate";s:6:"accept";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:10:"user-agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:25:"upgrade-insecure-requests";s:1:"1";s:13:"cache-control";s:9:"max-age=0";s:10:"connection";s:10:"keep-alive";s:4:"host";s:13:"skill-hub.loc";s:14:"content-length";s:0:"";s:12:"content-type";s:0:"";}s:15:"responseHeaders";a:8:{s:10:"Set-Cookie";s:60:"PHPSESSID=47f8d75ca172651717bb4ea61deed986; path=/; HttpOnly";s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:12:"Content-Type";s:24:"text/html; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"6875000d2b93e";s:16:"X-Debug-Duration";s:5:"3,868";s:12:"X-Debug-Link";s:37:"/debug/default/view?tag=6875000d2b93e";}s:5:"route";s:10:"site/index";s:6:"action";s:50:"frontend\controllers\SiteController::actionIndex()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:3:"GET";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:0:{}s:6:"SERVER";a:82:{s:17:"PHP_ENABLE_XDEBUG";s:1:"0";s:11:"PHP_USER_ID";s:2:"33";s:8:"HOSTNAME";s:12:"e2c384a79b49";s:11:"PHP_VERSION";s:6:"8.0.30";s:11:"PHP_INI_DIR";s:18:"/usr/local/etc/php";s:8:"GPG_KEYS";s:163:"1729F83938DA44E27BA0F4D3DBDB397470D12172 BFDDD28642824F8118EF77909B67A5C12229118F 2C16C765DBE54A088130F1BC4B9B5F600B55F3B4 39B641343D8C104B2B146DC3F9C39DC0B9698544";s:11:"PHP_LDFLAGS";s:12:"-Wl,-O1 -pie";s:3:"PWD";s:4:"/app";s:4:"HOME";s:8:"/var/www";s:10:"PHP_SHA256";s:64:"216ab305737a5d392107112d618a755dc5df42058226f1670e9db90e77d777d9";s:11:"PHPIZE_DEPS";s:76:"autoconf 		dpkg-dev 		file 		g++ 		gcc 		libc-dev 		make 		pkg-config 		re2c";s:4:"TERM";s:5:"linux";s:7:"PHP_URL";s:51:"https://www.php.net/distributions/php-8.0.30.tar.xz";s:5:"SHLVL";s:1:"0";s:24:"COMPOSER_ALLOW_SUPERUSER";s:1:"1";s:10:"PHP_CFLAGS";s:83:"-fstack-protector-strong -fpic -fpie -O2 -D_LARGEFILE_SOURCE -D_FILE_OFFSET_BITS=64";s:4:"PATH";s:108:"/app:/app/vendor/bin:/root/.composer/vendor/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin";s:11:"PHP_ASC_URL";s:55:"https://www.php.net/distributions/php-8.0.30.tar.xz.asc";s:12:"PHP_CPPFLAGS";s:83:"-fstack-protector-strong -fpic -fpie -O2 -D_LARGEFILE_SOURCE -D_FILE_OFFSET_BITS=64";s:4:"USER";s:8:"www-data";s:20:"HTTP_ACCEPT_LANGUAGE";s:35:"ru-RU,ru;q=0.9,en-US;q=0.8,en;q=0.7";s:20:"HTTP_ACCEPT_ENCODING";s:13:"gzip, deflate";s:11:"HTTP_ACCEPT";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:15:"HTTP_USER_AGENT";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:30:"HTTP_UPGRADE_INSECURE_REQUESTS";s:1:"1";s:18:"HTTP_CACHE_CONTROL";s:9:"max-age=0";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:9:"HTTP_HOST";s:13:"skill-hub.loc";s:15:"REDIRECT_STATUS";s:3:"200";s:11:"SERVER_NAME";s:13:"skill-hub.loc";s:11:"SERVER_PORT";s:2:"80";s:11:"SERVER_ADDR";s:10:"**********";s:11:"REMOTE_PORT";s:5:"44862";s:11:"REMOTE_ADDR";s:10:"**********";s:15:"SERVER_SOFTWARE";s:12:"nginx/1.28.0";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:14:"REQUEST_SCHEME";s:4:"http";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:13:"DOCUMENT_ROOT";s:17:"/app/frontend/web";s:12:"DOCUMENT_URI";s:10:"/index.php";s:11:"REQUEST_URI";s:1:"/";s:11:"SCRIPT_NAME";s:10:"/index.php";s:14:"CONTENT_LENGTH";s:0:"";s:12:"CONTENT_TYPE";s:0:"";s:14:"REQUEST_METHOD";s:3:"GET";s:12:"QUERY_STRING";s:0:"";s:15:"SCRIPT_FILENAME";s:27:"/app/frontend/web/index.php";s:9:"FCGI_ROLE";s:9:"RESPONDER";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:1752498186.442499;s:12:"REQUEST_TIME";i:1752498186;s:4:"argv";a:0:{}s:4:"argc";i:0;s:9:"YII_DEBUG";s:4:"true";s:7:"YII_ENV";s:3:"dev";s:15:"APP_MAINTENANCE";s:1:"0";s:11:"LINK_ASSETS";s:4:"true";s:6:"DB_DSN";s:42:"pgsql:host=db;port=5432;dbname=skillhub_db";s:11:"DB_USERNAME";s:8:"postgres";s:11:"DB_PASSWORD";s:8:"postgres";s:15:"DB_TABLE_PREFIX";s:0:"";s:11:"TEST_DB_DSN";s:38:"pgsql:host=db;port=5432;dbname=test_db";s:16:"TEST_DB_USERNAME";s:8:"postgres";s:16:"TEST_DB_PASSWORD";s:8:"postgres";s:20:"TEST_DB_TABLE_PREFIX";s:0:"";s:13:"API_HOST_INFO";s:37:"http://api.yii2-starter-kit.localhost";s:18:"FRONTEND_HOST_INFO";s:33:"http://yii2-starter-kit.localhost";s:17:"BACKEND_HOST_INFO";s:41:"http://backend.yii2-starter-kit.localhost";s:17:"STORAGE_HOST_INFO";s:41:"http://storage.yii2-starter-kit.localhost";s:9:"SMTP_HOST";s:11:"mailcatcher";s:9:"SMTP_PORT";s:4:"1025";s:30:"FRONTEND_COOKIE_VALIDATION_KEY";s:15:"<generated_key>";s:29:"BACKEND_COOKIE_VALIDATION_KEY";s:15:"<generated_key>";s:11:"ADMIN_EMAIL";s:32:"<EMAIL>";s:11:"ROBOT_EMAIL";s:32:"<EMAIL>";s:16:"GITHUB_CLIENT_ID";s:14:"your-client-id";s:20:"GITHUB_CLIENT_SECRET";s:18:"your-client-secret";s:14:"GLIDE_SIGN_KEY";s:15:"<generated_key>";s:20:"GLIDE_MAX_IMAGE_SIZE";s:7:"4000000";s:29:"COMPOSE_CONVERT_WINDOWS_PATHS";s:1:"1";s:18:"TELEGRAM_BOT_TOKEN";s:46:"**********************************************";s:25:"TELEGRAM_CONTACT_GROUP_ID";s:14:"-1002758503392";}s:3:"GET";a:0:{}s:4:"POST";a:0:{}s:6:"COOKIE";a:0:{}s:5:"FILES";a:0:{}s:7:"SESSION";a:1:{s:7:"__flash";a:0:{}}}";s:4:"user";s:2:"N;";s:5:"asset";s:6:"a:0:{}";s:7:"summary";a:12:{s:3:"tag";s:13:"6875000d2b93e";s:3:"url";s:21:"http://skill-hub.loc/";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:10:"**********";s:4:"time";d:1752498186.442499;s:10:"statusCode";i:500;s:8:"sqlCount";i:10;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:3119896;s:14:"processingTime";d:4.0936689376831055;}s:10:"exceptions";a:0:{}}